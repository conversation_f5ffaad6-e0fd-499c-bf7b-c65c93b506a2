# Boyou BBS 6.1 安全配置
# Apache Web服务器安全头部和访问控制

# 启用重写引擎
RewriteEngine On

# 安全头部设置
<IfModule mod_headers.c>
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # 防止点击劫持攻击
    Header always set X-Frame-Options DENY
    
    # 启用XSS保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 引用者策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 内容安全策略 (基础版本)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';"
    
    # 移除服务器信息
    Header always unset Server
    Header always unset X-Powered-By
    
    # 缓存控制
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# 保护敏感文件和目录
<Files "*.db">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.conf">
    Require all denied
</Files>

<Files "*.inc">
    Require all denied
</Files>

<Files ".env">
    Require all denied
</Files>

<Files "composer.json">
    Require all denied
</Files>

<Files "composer.lock">
    Require all denied
</Files>

<Files "package.json">
    Require all denied
</Files>

# 保护配置目录
<DirectoryMatch "^.*/conf/">
    Require all denied
</DirectoryMatch>

# 保护数据目录
<DirectoryMatch "^.*/data/">
    Require all denied
</DirectoryMatch>

# 保护日志目录
<DirectoryMatch "^.*/log/">
    Require all denied
</DirectoryMatch>

# 保护临时目录中的敏感文件
<DirectoryMatch "^.*/tmp/">
    <Files "*.php">
        Require all denied
    </Files>
</DirectoryMatch>

# 保护版本控制目录
<DirectoryMatch "\.git">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "\.svn">
    Require all denied
</DirectoryMatch>

# 保护备份文件
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# URL重写规则 (如果需要美化URL)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ index.php?mod=$1 [QSA,L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/([^/]+)/?$ index.php?mod=$1&action=$2 [QSA,L]

# 防止直接访问PHP文件 (除了入口文件)
<FilesMatch "\.php$">
    <If "%{REQUEST_URI} !~ m#^/(index\.php|admin/index\.php)$#">
        Require all denied
    </If>
</FilesMatch>

# 限制请求方法
<LimitExcept GET POST HEAD>
    Require all denied
</LimitExcept>

# 防止目录浏览
Options -Indexes

# 防止服务器签名
ServerTokens Prod

# 文件上传限制
<Directory "upload/">
    # 禁止执行PHP文件
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
    
    # 只允许特定文件类型
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$">
        Require all granted
    </FilesMatch>
    
    # 其他文件类型拒绝访问
    <FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$).*$">
        Require all denied
    </FilesMatch>
</Directory>

# 错误页面
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html
