# XSS防护使用指南

## 模板中的安全输出

### 1. 基本变量输出
```html
<!-- 不安全 -->
{$username}

<!-- 安全 -->
{$username|htmlspecialchars}
或
<?php echo safe_echo($username); ?>
```

### 2. 属性值输出
```html
<!-- 不安全 -->
<input value="{$value}">

<!-- 安全 -->
<input value="{$value|htmlspecialchars}">
或
<input value="<?php echo safe_attr($value); ?>">
```

### 3. URL输出
```html
<!-- 不安全 -->
<a href="{$url}">链接</a>

<!-- 安全 -->
<a href="<?php echo safe_url($url); ?>">链接</a>
```

### 4. JavaScript中的变量
```html
<!-- 不安全 -->
<script>var data = "{$data}";</script>

<!-- 安全 -->
<script>var data = <?php echo safe_js($data); ?>;</script>
```

### 5. 富文本内容
```html
<!-- 不安全 -->
{$content}

<!-- 安全（允许部分HTML） -->
<?php echo safe_html($content); ?>

<!-- 安全（完全转义） -->
<?php echo safe_echo($content); ?>
```

## PHP代码中的安全输出

### 1. 直接输出
```php
// 不安全
echo $_GET['name'];

// 安全
echo htmlspecialchars($_GET['name'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
// 或
echo xn_input_filter($_GET['name']);
```

### 2. 使用过滤器
```php
// 过滤不同类型的输入
$username = xn_input_filter($_POST['username'], 'string');
$email = xn_input_filter($_POST['email'], 'email');
$age = xn_input_filter($_POST['age'], 'int');
```

## 安全函数说明

- `safe_echo($value)` - 安全输出文本
- `safe_attr($value)` - 安全输出HTML属性值
- `safe_url($url)` - 安全输出URL
- `safe_js($value)` - 安全输出到JavaScript
- `safe_css($value)` - 安全输出CSS值
- `safe_html($html)` - 安全输出富文本（允许部分HTML标签）
- `xn_input_filter($input, $type)` - 输入过滤和验证

## 注意事项

1. 默认情况下，所有用户输入都应该被转义
2. 只有在确实需要输出HTML时才使用 `safe_html()`
3. 在JavaScript中输出变量时必须使用 `safe_js()`
4. URL必须经过验证和过滤
5. 定期检查和更新安全过滤规则
