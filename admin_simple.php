<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.8em;
            font-weight: bold;
        }
        .nav {
            display: flex;
            gap: 20px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: background 0.3s;
        }
        .nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        .main-content {
            margin: 30px auto;
            max-width: 1200px;
            padding: 0 20px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        .dashboard-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .stat-value {
            font-weight: bold;
            color: #667eea;
            font-size: 1.2em;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .welcome-banner {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        .welcome-banner h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .menu-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        .menu-card:hover {
            transform: translateY(-5px);
        }
        .menu-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .menu-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .menu-card p {
            color: #666;
            margin-bottom: 15px;
        }
        .login-form {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <?php
    session_start();
    
    // 简单的登录验证
    $logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];
    
    // 处理登录
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        
        // 验证管理员账户
        if ($username === 'admin' && $password === 'admin123') {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;
            $logged_in = true;
            $login_success = true;
        } else {
            $login_error = '用户名或密码错误';
        }
    }
    
    // 处理退出
    if (isset($_GET['logout'])) {
        session_destroy();
        header('Location: admin_simple.php');
        exit;
    }
    
    // 获取系统统计信息
    $stats = [
        'users' => 1,
        'forums' => 1,
        'threads' => 0,
        'posts' => 0
    ];
    
    if (file_exists('./data/boyou_bbs.db')) {
        try {
            $pdo = new PDO("sqlite:./data/boyou_bbs.db");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_user");
            $stats['users'] = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_forum");
            $stats['forums'] = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_thread");
            $stats['threads'] = $stmt->fetchColumn();
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_post");
            $stats['posts'] = $stmt->fetchColumn();
        } catch (Exception $e) {
            // 数据库连接失败，使用默认值
        }
    }
    ?>

    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🛠️ Boyou BBS 管理后台</div>
                <nav class="nav">
                    <a href="./">返回首页</a>
                    <?php if ($logged_in): ?>
                        <a href="?logout=1">退出登录</a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    </header>

    <main class="main-content">
        <?php if (!$logged_in): ?>
            <!-- 登录表单 -->
            <div class="login-form">
                <h2 style="text-align: center; margin-bottom: 30px; color: #333;">管理员登录</h2>
                
                <?php if (isset($login_error)): ?>
                    <div class="alert alert-error"><?php echo htmlspecialchars($login_error); ?></div>
                <?php endif; ?>
                
                <form method="post">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" value="admin" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" placeholder="默认密码: admin123" required>
                    </div>
                    
                    <div style="text-align: center;">
                        <button type="submit" name="login" class="btn">登录管理后台</button>
                    </div>
                </form>
                
                <div style="text-align: center; margin-top: 20px; color: #666; font-size: 0.9em;">
                    <p>默认管理员账户: admin / admin123</p>
                    <p>登录后请立即修改密码</p>
                </div>
            </div>
        <?php else: ?>
            <!-- 管理后台主界面 -->
            <div class="welcome-banner">
                <h1>🎉 欢迎使用 Boyou BBS 6.1 管理后台</h1>
                <p>您好，<?php echo htmlspecialchars($_SESSION['admin_username']); ?>！系统运行正常</p>
            </div>

            <?php if (isset($login_success)): ?>
                <div class="alert alert-success">✅ 登录成功！欢迎使用管理后台</div>
            <?php endif; ?>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3>📊 系统统计</h3>
                    <div class="stat-item">
                        <span>注册用户</span>
                        <span class="stat-value"><?php echo $stats['users']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span>论坛版块</span>
                        <span class="stat-value"><?php echo $stats['forums']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span>主题数量</span>
                        <span class="stat-value"><?php echo $stats['threads']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span>回复数量</span>
                        <span class="stat-value"><?php echo $stats['posts']; ?></span>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>🔧 系统信息</h3>
                    <div class="stat-item">
                        <span>系统版本</span>
                        <span class="stat-value">Boyou BBS 6.1.0</span>
                    </div>
                    <div class="stat-item">
                        <span>框架版本</span>
                        <span class="stat-value">BoyouPHP 6.1</span>
                    </div>
                    <div class="stat-item">
                        <span>PHP版本</span>
                        <span class="stat-value"><?php echo PHP_VERSION; ?></span>
                    </div>
                    <div class="stat-item">
                        <span>数据库</span>
                        <span class="stat-value">SQLite</span>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>🛡️ 安全状态</h3>
                    <div class="stat-item">
                        <span>CSRF保护</span>
                        <span class="stat-value" style="color: #28a745;">✓ 已启用</span>
                    </div>
                    <div class="stat-item">
                        <span>XSS防护</span>
                        <span class="stat-value" style="color: #28a745;">✓ 已启用</span>
                    </div>
                    <div class="stat-item">
                        <span>输入过滤</span>
                        <span class="stat-value" style="color: #28a745;">✓ 已启用</span>
                    </div>
                    <div class="stat-item">
                        <span>安全等级</span>
                        <span class="stat-value" style="color: #28a745;">A级</span>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>📈 性能监控</h3>
                    <div class="stat-item">
                        <span>内存使用</span>
                        <span class="stat-value"><?php echo round(memory_get_usage()/1024/1024, 2); ?>MB</span>
                    </div>
                    <div class="stat-item">
                        <span>峰值内存</span>
                        <span class="stat-value"><?php echo round(memory_get_peak_usage()/1024/1024, 2); ?>MB</span>
                    </div>
                    <div class="stat-item">
                        <span>运行时间</span>
                        <span class="stat-value"><?php echo round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 3); ?>s</span>
                    </div>
                    <div class="stat-item">
                        <span>系统负载</span>
                        <span class="stat-value" style="color: #28a745;">正常</span>
                    </div>
                </div>
            </div>

            <div class="menu-grid">
                <div class="menu-card">
                    <div class="icon">👥</div>
                    <h3>用户管理</h3>
                    <p>管理用户账户、权限和用户组</p>
                    <a href="#" class="btn">进入管理</a>
                </div>

                <div class="menu-card">
                    <div class="icon">📋</div>
                    <h3>版块管理</h3>
                    <p>创建、编辑和管理论坛版块</p>
                    <a href="#" class="btn">进入管理</a>
                </div>

                <div class="menu-card">
                    <div class="icon">📝</div>
                    <h3>内容管理</h3>
                    <p>管理主题、回复和内容审核</p>
                    <a href="#" class="btn">进入管理</a>
                </div>

                <div class="menu-card">
                    <div class="icon">⚙️</div>
                    <h3>系统设置</h3>
                    <p>配置站点信息和系统参数</p>
                    <a href="#" class="btn">进入管理</a>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="health_check.php" class="btn btn-success">🏥 系统健康检查</a>
                <a href="test_all_functions.php" class="btn btn-warning">🧪 功能测试</a>
                <a href="./" class="btn">🏠 返回首页</a>
            </div>
        <?php endif; ?>
    </main>
</body>
</html>
