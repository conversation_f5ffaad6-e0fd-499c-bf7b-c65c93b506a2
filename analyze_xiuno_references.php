<?php

echo "=== 分析项目中剩余的 xiuno 引用 ===\n\n";

// 需要检查的目录
$directories = [
    '.',
    'admin',
    'install',
    'plugin',
    'view',
    'lang',
    'conf',
    'tool',
    'tmp',
    'boyouphp'
];

// 需要检查的文件扩展名
$extensions = ['php', 'htm', 'html', 'js', 'css', 'md', 'txt', 'json'];

// 已知的替换映射
$replacements = [
    'xiuno' => 'boyou',
    'Xiuno' => 'Boyou', 
    'XIUNO' => 'BOYOU',
    'boyouphp' => 'boyouphp',
    'XiunoPHP' => 'BoyouPHP',
    'XIUNOPHP' => 'BOYOUPHP'
];

// 需要保留的xiuno引用（不应该替换的）
$preservePatterns = [
    '/xiuno\.js/', // 如果还有对boyou.js的引用，应该替换为boyou.js
    '/boyouphp/', // 目录引用应该替换为boyouphp
    '/xiuno_/', // 数据库表前缀等
];

$xiunoReferences = [];
$alreadyReplaced = [];
$needsReplacement = [];

echo "开始扫描文件...\n\n";

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        echo "跳过不存在的目录: $dir\n";
        continue;
    }
    
    echo "扫描目录: $dir\n";
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if (!$file->isFile()) continue;
        
        $extension = strtolower($file->getExtension());
        if (!in_array($extension, $extensions)) continue;
        
        $filePath = $file->getPathname();
        $content = file_get_contents($filePath);
        
        if ($content === false) continue;
        
        // 查找所有xiuno相关的引用
        $patterns = [
            '/\bxiuno\b/i',
            '/\bboyouphp\b/i',
            '/xiuno\.js/',
            '/xiuno\.com/',
            '/xiuno_[a-zA-Z_]+/',
            '/XIUNO[A-Z_]*/',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $matchText = $match[0];
                    $offset = $match[1];
                    
                    // 计算行号
                    $lineNumber = substr_count(substr($content, 0, $offset), "\n") + 1;
                    
                    // 获取上下文
                    $lines = explode("\n", $content);
                    $contextStart = max(0, $lineNumber - 2);
                    $contextEnd = min(count($lines) - 1, $lineNumber + 1);
                    $context = array_slice($lines, $contextStart, $contextEnd - $contextStart + 1);
                    
                    $reference = [
                        'file' => $filePath,
                        'line' => $lineNumber,
                        'match' => $matchText,
                        'context' => $context,
                        'context_start_line' => $contextStart + 1
                    ];
                    
                    // 检查是否已经被替换为boyou
                    $surroundingText = strtolower(implode(' ', $context));
                    if (strpos($surroundingText, 'boyou') !== false) {
                        $alreadyReplaced[] = $reference;
                    } else {
                        $needsReplacement[] = $reference;
                    }
                    
                    $xiunoReferences[] = $reference;
                }
            }
        }
    }
}

echo "\n=== 分析结果 ===\n";
echo "总共找到 xiuno 引用: " . count($xiunoReferences) . " 处\n";
echo "已替换为 boyou: " . count($alreadyReplaced) . " 处\n";
echo "需要替换: " . count($needsReplacement) . " 处\n";

if (!empty($needsReplacement)) {
    echo "\n=== 需要替换的引用 ===\n";
    
    $fileGroups = [];
    foreach ($needsReplacement as $ref) {
        $fileGroups[$ref['file']][] = $ref;
    }
    
    foreach ($fileGroups as $file => $refs) {
        echo "\n文件: $file\n";
        foreach ($refs as $ref) {
            echo "  行 {$ref['line']}: {$ref['match']}\n";
            echo "  上下文:\n";
            foreach ($ref['context'] as $i => $line) {
                $lineNum = $ref['context_start_line'] + $i;
                $marker = ($lineNum == $ref['line']) ? '>>> ' : '    ';
                echo "    $marker$lineNum: " . trim($line) . "\n";
            }
            echo "\n";
        }
    }
}

if (!empty($alreadyReplaced)) {
    echo "\n=== 已替换的引用（确认） ===\n";
    
    $fileGroups = [];
    foreach ($alreadyReplaced as $ref) {
        $fileGroups[$ref['file']][] = $ref;
    }
    
    foreach ($fileGroups as $file => $refs) {
        echo "\n文件: $file (" . count($refs) . " 处)\n";
        foreach (array_slice($refs, 0, 3) as $ref) { // 只显示前3个
            echo "  行 {$ref['line']}: {$ref['match']} (已有boyou替换)\n";
        }
        if (count($refs) > 3) {
            echo "  ... 还有 " . (count($refs) - 3) . " 处\n";
        }
    }
}

// 生成替换建议
echo "\n=== 替换建议 ===\n";

$replacementSuggestions = [];
foreach ($needsReplacement as $ref) {
    $match = $ref['match'];
    $suggestion = $match;
    
    // 应用替换规则
    foreach ($replacements as $from => $to) {
        if (stripos($match, $from) !== false) {
            $suggestion = str_ireplace($from, $to, $match);
            break;
        }
    }
    
    if ($suggestion !== $match) {
        $key = $ref['file'] . ':' . $ref['line'];
        $replacementSuggestions[$key] = [
            'file' => $ref['file'],
            'line' => $ref['line'],
            'from' => $match,
            'to' => $suggestion
        ];
    }
}

if (!empty($replacementSuggestions)) {
    echo "建议的替换操作:\n";
    foreach ($replacementSuggestions as $suggestion) {
        echo "  {$suggestion['file']}:{$suggestion['line']}\n";
        echo "    {$suggestion['from']} → {$suggestion['to']}\n";
    }
} else {
    echo "没有找到需要替换的引用，或者所有引用都已经正确替换。\n";
}

// 检查特殊情况
echo "\n=== 特殊检查 ===\n";

// 检查是否还有xiunophp目录引用
$xiunophpRefs = array_filter($needsReplacement, function($ref) {
    return stripos($ref['match'], 'boyouphp') !== false;
});

if (!empty($xiunophpRefs)) {
    echo "⚠️  发现 xiunophp 目录引用，需要替换为 boyouphp:\n";
    foreach ($xiunophpRefs as $ref) {
        echo "  {$ref['file']}:{$ref['line']} - {$ref['match']}\n";
    }
} else {
    echo "✓ 没有发现 xiunophp 目录引用\n";
}

// 检查是否还有boyou.js引用
$xiunoJsRefs = array_filter($needsReplacement, function($ref) {
    return stripos($ref['match'], 'boyou.js') !== false;
});

if (!empty($xiunoJsRefs)) {
    echo "⚠️  发现 boyou.js 文件引用，需要替换为 boyou.js:\n";
    foreach ($xiunoJsRefs as $ref) {
        echo "  {$ref['file']}:{$ref['line']} - {$ref['match']}\n";
    }
} else {
    echo "✓ 没有发现 boyou.js 文件引用\n";
}

echo "\n=== 分析完成 ===\n";

// 保存分析结果到文件
$reportData = [
    'total_references' => count($xiunoReferences),
    'already_replaced' => count($alreadyReplaced),
    'needs_replacement' => count($needsReplacement),
    'replacement_suggestions' => array_values($replacementSuggestions),
    'xiuno_references' => $needsReplacement
];

file_put_contents('xiuno_analysis_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n详细分析报告已保存到: xiuno_analysis_report.json\n";

?>
