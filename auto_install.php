<?php

echo "=== Boyou BBS 6.1 自动安装脚本 ===\n\n";

// 检查是否已安装
if (file_exists('./data/install.lock')) {
    echo "✓ 系统已经安装，安装时间: " . file_get_contents('./data/install.lock') . "\n";
    echo "如需重新安装，请删除 data/install.lock 文件\n";
    exit;
}

echo "开始自动安装 Boyou BBS 6.1...\n\n";

// 1. 创建必要的目录
echo "1. 创建必要的目录...\n";
$dirs = ['data', 'upload', 'upload/attach', 'upload/avatar', 'upload/forum', 'upload/tmp', 'log', 'tmp'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✓ 创建目录: $dir\n";
        } else {
            echo "✗ 创建目录失败: $dir\n";
            exit(1);
        }
    } else {
        echo "✓ 目录已存在: $dir\n";
    }
}

// 2. 创建配置文件
echo "\n2. 创建配置文件...\n";

$config_content = "<?php\n\n";
$config_content .= "// Boyou BBS 6.1 配置文件\n";
$config_content .= "// 自动生成于 " . date('Y-m-d H:i:s') . "\n\n";
$config_content .= "\$conf = [];\n\n";
$config_content .= "// 站点信息\n";
$config_content .= "\$conf['sitename'] = 'Boyou BBS';\n";
$config_content .= "\$conf['siteurl'] = 'http://localhost:8000';\n";
$config_content .= "\$conf['version'] = '6.1.0';\n\n";
$config_content .= "// 数据库配置（使用SQLite）\n";
$config_content .= "\$conf['db']['sqlite']['master']['path'] = './data/boyou_bbs.db';\n";
$config_content .= "\$conf['db']['tablepre'] = 'bbs_';\n\n";
$config_content .= "// 缓存配置\n";
$config_content .= "\$conf['cache']['type'] = 'file';\n";
$config_content .= "\$conf['cache']['dir'] = './tmp/';\n\n";
$config_content .= "// 其他配置\n";
$config_content .= "\$conf['tmp_path'] = './tmp/';\n";
$config_content .= "\$conf['upload_path'] = './upload/';\n";
$config_content .= "\$conf['log_path'] = './log/';\n\n";
$config_content .= "return \$conf;\n";
$config_content .= "?>";

if (file_put_contents('./conf/conf.php', $config_content)) {
    echo "✓ 配置文件创建成功: conf/conf.php\n";
} else {
    echo "✗ 配置文件创建失败\n";
    exit(1);
}

// 3. 创建SQLite数据库
echo "\n3. 创建SQLite数据库...\n";

try {
    $db_path = './data/boyou_bbs.db';
    $pdo = new PDO("sqlite:$db_path");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ SQLite数据库连接成功\n";
    
    // 创建数据库表
    $sql_tables = "
    CREATE TABLE IF NOT EXISTS bbs_user (
        uid INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(32) NOT NULL UNIQUE,
        password VARCHAR(64) NOT NULL,
        email VARCHAR(64) NOT NULL UNIQUE,
        create_date INTEGER NOT NULL,
        login_date INTEGER NOT NULL,
        group_id INTEGER NOT NULL DEFAULT 1
    );
    
    CREATE TABLE IF NOT EXISTS bbs_group (
        group_id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(32) NOT NULL,
        credit_from INTEGER NOT NULL DEFAULT 0,
        credit_to INTEGER NOT NULL DEFAULT 0
    );
    
    CREATE TABLE IF NOT EXISTS bbs_forum (
        fid INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(64) NOT NULL,
        rank INTEGER NOT NULL DEFAULT 0,
        threads INTEGER NOT NULL DEFAULT 0,
        posts INTEGER NOT NULL DEFAULT 0
    );
    
    CREATE TABLE IF NOT EXISTS bbs_thread (
        tid INTEGER PRIMARY KEY AUTOINCREMENT,
        fid INTEGER NOT NULL,
        uid INTEGER NOT NULL,
        subject VARCHAR(128) NOT NULL,
        create_date INTEGER NOT NULL,
        last_date INTEGER NOT NULL,
        posts INTEGER NOT NULL DEFAULT 1,
        views INTEGER NOT NULL DEFAULT 0
    );
    
    CREATE TABLE IF NOT EXISTS bbs_post (
        pid INTEGER PRIMARY KEY AUTOINCREMENT,
        tid INTEGER NOT NULL,
        uid INTEGER NOT NULL,
        create_date INTEGER NOT NULL,
        message TEXT NOT NULL
    );
    ";
    
    $pdo->exec($sql_tables);
    echo "✓ 数据库表创建成功\n";
    
    // 插入默认数据
    $pdo->exec("INSERT OR IGNORE INTO bbs_group VALUES (1, '管理员', 0, 999999), (2, '版主', 100, 999999), (3, '会员', 0, 99)");
    $pdo->exec("INSERT OR IGNORE INTO bbs_forum VALUES (1, '默认版块', 1, 0, 0)");
    echo "✓ 默认数据插入成功\n";
    
    // 创建管理员账户
    $admin_username = 'admin';
    $admin_password = 'admin123';
    $admin_email = '<EMAIL>';
    $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
    $create_time = time();
    
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO bbs_user (username, password, email, create_date, login_date, group_id) VALUES (?, ?, ?, ?, ?, 1)");
    $stmt->execute([$admin_username, $password_hash, $admin_email, $create_time, $create_time]);
    
    echo "✓ 管理员账户创建成功\n";
    echo "  用户名: $admin_username\n";
    echo "  密码: $admin_password\n";
    echo "  邮箱: $admin_email\n";
    
} catch (Exception $e) {
    echo "✗ 数据库操作失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. 创建安装锁文件
echo "\n4. 创建安装锁文件...\n";
$install_time = date('Y-m-d H:i:s');
if (file_put_contents('./data/install.lock', $install_time)) {
    echo "✓ 安装锁文件创建成功\n";
} else {
    echo "✗ 安装锁文件创建失败\n";
    exit(1);
}

// 5. 测试系统功能
echo "\n5. 测试系统功能...\n";

// 测试配置文件加载
$conf = include './conf/conf.php';
if (is_array($conf) && isset($conf['sitename'])) {
    echo "✓ 配置文件加载正常\n";
} else {
    echo "✗ 配置文件加载失败\n";
}

// 测试数据库连接
try {
    $pdo = new PDO("sqlite:./data/boyou_bbs.db");
    $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_user");
    $user_count = $stmt->fetchColumn();
    echo "✓ 数据库连接正常，用户数: $user_count\n";
} catch (Exception $e) {
    echo "✗ 数据库连接测试失败\n";
}

// 测试框架加载
if (file_exists('./boyouphp/boyouphp.php')) {
    echo "✓ BoyouPHP框架文件存在\n";
} else {
    echo "✗ BoyouPHP框架文件缺失\n";
}

echo "\n=== 安装完成 ===\n";
echo "🎉 Boyou BBS 6.1 安装成功！\n\n";

echo "📋 安装信息:\n";
echo "  安装时间: $install_time\n";
echo "  系统版本: Boyou BBS 6.1.0\n";
echo "  框架版本: BoyouPHP 6.1\n";
echo "  数据库: SQLite\n";
echo "  管理员: $admin_username / $admin_password\n\n";

echo "🌐 访问地址:\n";
echo "  论坛首页: http://localhost:8000/\n";
echo "  管理后台: http://localhost:8000/admin/\n";
echo "  健康检查: http://localhost:8000/health_check.php\n\n";

echo "🛡️ 安全提醒:\n";
echo "  - 请立即修改管理员密码\n";
echo "  - 删除安装相关文件\n";
echo "  - 配置适当的文件权限\n";
echo "  - 在生产环境中启用HTTPS\n\n";

echo "🚀 下一步:\n";
echo "  1. 访问论坛首页查看效果\n";
echo "  2. 登录管理后台进行配置\n";
echo "  3. 创建版块和设置权限\n";
echo "  4. 测试所有功能模块\n\n";

echo "安装脚本执行完成！\n";

?>
