#!/bin/bash
# 自动备份脚本
# 建议添加到crontab: 0 2 * * * /path/to/backup.sh

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="boyou_bbs_backup_$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份文件
echo "开始备份文件..."
tar -czf "$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz" \
    --exclude="./backups" \
    --exclude="./log" \
    --exclude="./tmp" \
    --exclude="./upload/tmp" \
    .

# 备份数据库（需要配置数据库信息）
# mysqldump -u username -p password database_name > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"

# 删除7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "备份完成: $BACKUP_NAME"
