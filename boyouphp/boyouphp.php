<?php

/*
	BoyouPHP 6.1 只是定义了一些函数和全局变量，方便使用，并没有要求如何组织代码。
	采用静态语言编程风格，有利于 Zend 引擎的编译和 OPCache 缓存，支持 PHP 8.x
	基于 XiunoPHP 4.0 进行现代化改造
	1. 禁止使用 eval(), 正则表达式 e 修饰符
	2. 尽量避免 autoload
	3. 尽量避免 $$var 多重变量
	4. 尽量避免 PHP 高级特性 __call __set __get 等魔术方法，不利于错误排查
	5. 尽量采用函数封装功能，通过前缀区分模块
*/

!defined('DEBUG') AND define('DEBUG', 1); // 1: 开发模式， 2: 线上调试：日志记录，0: 关闭
!defined('APP_PATH') AND define('APP_PATH', './');
!defined('BOYOUPHP_PATH') AND define('BOYOUPHP_PATH', __DIR__.'/');

function_exists('ini_set') AND ini_set('display_errors', DEBUG ? '1' : '0');
error_reporting(DEBUG ? E_ALL : 0);
// 兼容 PHP 8.0+: magic quotes 已在 PHP 7.0 中移除
$get_magic_quotes_gpc = function_exists('get_magic_quotes_gpc') ? get_magic_quotes_gpc() : false;
$starttime = microtime(1);
$time = time();

// 头部，判断是否运行在命令行下
define('IN_CMD', !empty($_SERVER['SHELL']) || empty($_SERVER['REMOTE_ADDR']));
if(IN_CMD) {
	!isset($_SERVER['REMOTE_ADDR']) AND $_SERVER['REMOTE_ADDR'] = '';
	!isset($_SERVER['REQUEST_URI']) AND $_SERVER['REQUEST_URI'] = '';
	!isset($_SERVER['REQUEST_METHOD']) AND $_SERVER['REQUEST_METHOD'] = 'GET';
} else {
	header("Content-type: text/html; charset=utf-8");
	//header("Cache-Control: max-age=0;"); // 手机返回的时候回导致刷新
	//header("Cache-Control: no-store;");
	//header("X-Powered-By: BoyouPHP 6.1");
}

// hook boyouphp_include_before.php

// ----------------------------------------------------------> db cache class

// 数据库类
if(file_exists(BOYOUPHP_PATH.'db_mysql.class.php')) include BOYOUPHP_PATH.'db_mysql.class.php';
if(file_exists(BOYOUPHP_PATH.'db_pdo_mysql.class.php')) include BOYOUPHP_PATH.'db_pdo_mysql.class.php';
if(file_exists(BOYOUPHP_PATH.'db_pdo_sqlite.class.php')) include BOYOUPHP_PATH.'db_pdo_sqlite.class.php';

// 缓存类
if(file_exists(BOYOUPHP_PATH.'cache_apc.class.php')) include BOYOUPHP_PATH.'cache_apc.class.php';
if(file_exists(BOYOUPHP_PATH.'cache_memcached.class.php')) include BOYOUPHP_PATH.'cache_memcached.class.php';
if(file_exists(BOYOUPHP_PATH.'cache_mysql.class.php')) include BOYOUPHP_PATH.'cache_mysql.class.php';
if(file_exists(BOYOUPHP_PATH.'cache_redis.class.php')) include BOYOUPHP_PATH.'cache_redis.class.php';
if(file_exists(BOYOUPHP_PATH.'cache_xcache.class.php')) include BOYOUPHP_PATH.'cache_xcache.class.php';
if(file_exists(BOYOUPHP_PATH.'cache_yac.class.php')) include BOYOUPHP_PATH.'cache_yac.class.php';

// ----------------------------------------------------------> 全局函数

// 功能函数
if(file_exists(BOYOUPHP_PATH.'db.func.php')) include BOYOUPHP_PATH.'db.func.php';
if(file_exists(BOYOUPHP_PATH.'cache.func.php')) include BOYOUPHP_PATH.'cache.func.php';
if(file_exists(BOYOUPHP_PATH.'image.func.php')) include BOYOUPHP_PATH.'image.func.php';
if(file_exists(BOYOUPHP_PATH.'array.func.php')) include BOYOUPHP_PATH.'array.func.php';
if(file_exists(BOYOUPHP_PATH.'xn_encrypt.func.php')) include BOYOUPHP_PATH.'xn_encrypt.func.php';
if(file_exists(BOYOUPHP_PATH.'misc.func.php')) include BOYOUPHP_PATH.'misc.func.php';

// 其他可选功能文件
if(file_exists(BOYOUPHP_PATH.'xn_html_safe.func.php')) include BOYOUPHP_PATH.'xn_html_safe.func.php';
if(file_exists(BOYOUPHP_PATH.'xn_send_mail.func.php')) include BOYOUPHP_PATH.'xn_send_mail.func.php';
if(file_exists(BOYOUPHP_PATH.'xn_zip.func.php')) include BOYOUPHP_PATH.'xn_zip.func.php';

// hook boyouphp_include_after.php

empty($conf) AND $conf = ['db'=>[], 'cache'=>[], 'tmp_path'=>'./', 'log_path'=>'./', 'timezone'=>'Asia/Shanghai'];
empty($conf['tmp_path']) AND $conf['tmp_path'] = ini_get('upload_tmp_dir');
empty($conf['log_path']) AND $conf['log_path'] = './';

$ip = ip();
$longip = ip2long($ip);
$longip < 0 AND $longip = sprintf("%u", $longip); // fix 32 位 OS 下溢出的问题
$useragent = _SERVER('HTTP_USER_AGENT');

// 语言包变量
!isset($lang) AND $lang = [];

// 全局的错误，非多线程下很方便。
$errno = 0;
$errstr = '';

// error_handle
// register_shutdown_function('xn_shutdown_handle');
DEBUG AND set_error_handler('error_handle', E_ALL);
empty($conf['timezone']) AND $conf['timezone'] = 'Asia/Shanghai';
date_default_timezone_set($conf['timezone']);

// 设置时区
function_exists('ini_set') AND ini_set('date.timezone', $conf['timezone']);

// 全局变量
$_G = [];

// 设置全局变量
function G($k = '', $v = '') {
	global $_G;
	if($k === '') {
		return $_G;
	} elseif($v === '') {
		return isset($_G[$k]) ? $_G[$k] : NULL;
	} else {
		$_G[$k] = $v;
	}
}

// 错误处理函数
function error_handle($errno, $errstr, $errfile, $errline) {
	if(!(error_reporting() & $errno)) return;
	
	$error_type = [
		E_ERROR => 'Fatal Error',
		E_WARNING => 'Warning',
		E_PARSE => 'Parse Error',
		E_NOTICE => 'Notice',
		E_CORE_ERROR => 'Core Error',
		E_CORE_WARNING => 'Core Warning',
		E_COMPILE_ERROR => 'Compile Error',
		E_COMPILE_WARNING => 'Compile Warning',
		E_USER_ERROR => 'User Error',
		E_USER_WARNING => 'User Warning',
		E_USER_NOTICE => 'User Notice',
		E_STRICT => 'Strict Standards',
		E_RECOVERABLE_ERROR => 'Recoverable Error',
		E_DEPRECATED => 'Deprecated',
		E_USER_DEPRECATED => 'User Deprecated'
	];
	
	$type = isset($error_type[$errno]) ? $error_type[$errno] : 'Unknown Error';
	
	if(DEBUG) {
		echo "<div style='background:#f8d7da;color:#721c24;padding:10px;margin:10px;border:1px solid #f5c6cb;border-radius:4px;'>";
		echo "<strong>$type</strong>: $errstr<br>";
		echo "File: $errfile Line: $errline<br>";
		echo "Time: " . date('Y-m-d H:i:s');
		echo "</div>";
	}
	
	// 记录错误日志
	$log_path = G('conf')['log_path'] ?? './';
	$log_file = $log_path . 'error_' . date('Y-m-d') . '.log';
	$log_entry = sprintf("[%s] %s: %s in %s:%d\n", date('Y-m-d H:i:s'), $type, $errstr, $errfile, $errline);
	@file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// 版本信息
define('BOYOUPHP_VERSION', '6.1');
define('BOYOU_BBS_VERSION', '6.1');

?>
