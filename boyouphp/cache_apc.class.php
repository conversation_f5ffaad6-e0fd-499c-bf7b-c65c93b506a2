<?php

/**
 * APC 缓存类
 * BoyouPHP 6.1 版本
 */
class cache_apc {
	
	public $conf = [];
	public $errno = 0;
	public $errstr = '';
	
	public function __construct($conf) {
		$this->conf = $conf;
		
		// 检查APC扩展
		if(!function_exists('apc_store') && !function_exists('apcu_store')) {
			$this->errno = -1;
			$this->errstr = 'APC/APCu extension not loaded';
		}
	}
	
	public function connect() {
		return function_exists('apc_store') || function_exists('apcu_store');
	}
	
	public function close() {
		return true;
	}
	
	public function set($key, $value, $life = 0) {
		try {
			if(function_exists('apcu_store')) {
				return apcu_store($key, $value, $life);
			} elseif(function_exists('apc_store')) {
				return apc_store($key, $value, $life);
			}
			return false;
		} catch (Exception $e) {
			$this->errno = $e->getCode();
			$this->errstr = $e->getMessage();
			return false;
		}
	}
	
	public function get($key) {
		try {
			if(function_exists('apcu_fetch')) {
				$success = false;
				$value = apcu_fetch($key, $success);
				return $success ? $value : null;
			} elseif(function_exists('apc_fetch')) {
				$success = false;
				$value = apc_fetch($key, $success);
				return $success ? $value : null;
			}
			return null;
		} catch (Exception $e) {
			$this->errno = $e->getCode();
			$this->errstr = $e->getMessage();
			return null;
		}
	}
	
	public function delete($key) {
		try {
			if(function_exists('apcu_delete')) {
				return apcu_delete($key);
			} elseif(function_exists('apc_delete')) {
				return apc_delete($key);
			}
			return false;
		} catch (Exception $e) {
			$this->errno = $e->getCode();
			$this->errstr = $e->getMessage();
			return false;
		}
	}
	
	public function truncate() {
		try {
			if(function_exists('apcu_clear_cache')) {
				return apcu_clear_cache();
			} elseif(function_exists('apc_clear_cache')) {
				return apc_clear_cache('user');
			}
			return false;
		} catch (Exception $e) {
			$this->errno = $e->getCode();
			$this->errstr = $e->getMessage();
			return false;
		}
	}
	
	public function stats() {
		try {
			if(function_exists('apcu_cache_info')) {
				return apcu_cache_info();
			} elseif(function_exists('apc_cache_info')) {
				return apc_cache_info('user');
			}
			return [];
		} catch (Exception $e) {
			return [];
		}
	}
	
	public function info() {
		try {
			$stats = $this->stats();
			return [
				'type' => 'apc',
				'version' => phpversion('apc') ?: phpversion('apcu'),
				'memory' => isset($stats['mem_size']) ? $stats['mem_size'] : 0,
				'keys' => isset($stats['num_entries']) ? $stats['num_entries'] : 0,
				'connected' => true
			];
		} catch (Exception $e) {
			return [
				'type' => 'apc',
				'connected' => false,
				'error' => $e->getMessage()
			];
		}
	}
}

?>
