<?php

/**
 * MySQL数据库操作类 - 使用MySQLi扩展
 * 支持主从分离、连接池、事务处理
 * BoyouPHP 6.1 现代化版本
 */
class db_mysql {

	public $conf = []; // 配置，可以支持主从
	public $rconf = []; // 读库配置
	public $wlink = null;  // 写连接 (mysqli resource)
	public $rlink = null;  // 读连接 (mysqli resource)
	public $link = null;   // 最后一次使用的连接
	public $errno = 0;
	public $errstr = '';
	public $sqls = [];
	public $tablepre = '';
	public $innodb_first = true;// 优先 InnoDB
	
	/**
	 * 构造函数
	 * @param array $conf 数据库配置
	 */
	public function __construct($conf) {
		$this->conf = $conf;
		$this->tablepre = $conf['master']['tablepre'] ?? '';
	}
	
	/**
	 * 根据配置文件连接数据库
	 * @param string $type master|slave
	 * @return mysqli|false
	 */
	public function connect($type = 'master') {
		if($type == 'master') {
			if($this->wlink) return $this->wlink;
			$conf = $this->conf['master'];
			$this->wlink = $this->_connect($conf);
			$this->link = $this->wlink;
			return $this->wlink;
		} else {
			if($this->rlink) return $this->rlink;
			// 随机选择一个从库
			if(isset($this->conf['slaves']) && !empty($this->conf['slaves'])) {
				$slaves = $this->conf['slaves'];
				$n = count($slaves);
				$i = mt_rand(0, $n - 1);
				$conf = $slaves[$i];
				$this->rconf = $conf;
			} else {
				$conf = $this->conf['master'];
				$this->rconf = $conf;
			}
			$this->rlink = $this->_connect($conf);
			$this->link = $this->rlink;
			return $this->rlink;
		}
	}
	
	/**
	 * 内部连接方法
	 * @param array $conf 连接配置
	 * @return mysqli|false
	 */
	private function _connect($conf) {
		$host = $conf['host'];
		$port = $conf['port'] ?? 3306;
		$user = $conf['user'];
		$password = $conf['password'];
		$name = $conf['name'];
		$charset = $conf['charset'] ?? 'utf8mb4';
		
		// 创建连接
		$link = new mysqli($host, $user, $password, $name, $port);
		
		if($link->connect_error) {
			$this->errno = $link->connect_errno;
			$this->errstr = $link->connect_error;
			return false;
		}
		
		// 设置字符集
		if(!$link->set_charset($charset)) {
			$this->errno = $link->errno;
			$this->errstr = $link->error;
			return false;
		}
		
		// 设置SQL模式
		$link->query("SET sql_mode=''");
		
		return $link;
	}
	
	/**
	 * 关闭数据库连接
	 * @return bool
	 */
	public function close() {
		if($this->wlink) {
			$this->wlink->close();
			$this->wlink = null;
		}
		if($this->rlink) {
			$this->rlink->close();
			$this->rlink = null;
		}
		$this->link = null;
		return true;
	}
	
	/**
	 * 执行查询
	 * @param string $sql SQL语句
	 * @param string $type 查询类型
	 * @return mysqli_result|bool
	 */
	public function query($sql, $type = '') {
		// 自动判断读写
		if(empty($type)) {
			$type = $this->_get_query_type($sql);
		}
		
		$link = ($type == 'SELECT') ? $this->connect('slave') : $this->connect('master');
		if(!$link) return false;
		
		$result = $link->query($sql);
		
		if(!$result) {
			$this->errno = $link->errno;
			$this->errstr = $link->error;
		}
		
		$this->sqls[] = $sql;
		return $result;
	}
	
	/**
	 * 执行更新/插入/删除
	 * @param string $sql SQL语句
	 * @return bool
	 */
	public function exec($sql) {
		$link = $this->connect('master');
		if(!$link) return false;
		
		$result = $link->query($sql);
		
		if(!$result) {
			$this->errno = $link->errno;
			$this->errstr = $link->error;
		}
		
		$this->sqls[] = $sql;
		return $result;
	}
	
	/**
	 * 获取查询结果
	 * @param mysqli_result $result 查询结果
	 * @return array|null
	 */
	public function fetch_array($result) {
		if(!$result) return null;
		return $result->fetch_assoc();
	}
	
	/**
	 * 获取结果行数
	 * @param mysqli_result $result 查询结果
	 * @return int
	 */
	public function num_rows($result) {
		if(!$result) return 0;
		return $result->num_rows;
	}
	
	/**
	 * 获取影响行数
	 * @return int
	 */
	public function affected_rows() {
		return $this->link ? $this->link->affected_rows : 0;
	}
	
	/**
	 * 获取插入ID
	 * @return int
	 */
	public function insert_id() {
		return $this->link ? $this->link->insert_id : 0;
	}
	
	/**
	 * 释放结果集
	 * @param mysqli_result $result 查询结果
	 * @return bool
	 */
	public function free_result($result) {
		if($result && $result instanceof mysqli_result) {
			$result->free();
		}
		return true;
	}
	
	/**
	 * 转义字符串
	 * @param string $s 要转义的字符串
	 * @return string
	 */
	public function escape($s) {
		$link = $this->link ? $this->link : $this->connect('master');
		if(!$link) return addslashes($s);
		
		return $link->real_escape_string($s);
	}
	
	/**
	 * 获取数据库版本
	 * @return string
	 */
	public function version() {
		$link = $this->link ? $this->link : $this->connect('master');
		if(!$link) return '';
		
		return $link->server_info;
	}
	
	/**
	 * 判断查询类型
	 * @param string $sql SQL语句
	 * @return string
	 */
	private function _get_query_type($sql) {
		$sql = trim($sql);
		$type = strtoupper(substr($sql, 0, 6));
		
		switch($type) {
			case 'SELECT':
			case 'SHOW  ':
			case 'DESCRI':
			case 'EXPLAI':
				return 'SELECT';
			default:
				return 'WRITE';
		}
	}
	
	/**
	 * 开始事务
	 * @return bool
	 */
	public function begin() {
		$link = $this->connect('master');
		if(!$link) return false;
		
		return $link->autocommit(false);
	}
	
	/**
	 * 提交事务
	 * @return bool
	 */
	public function commit() {
		if(!$this->link) return false;
		
		$result = $this->link->commit();
		$this->link->autocommit(true);
		return $result;
	}
	
	/**
	 * 回滚事务
	 * @return bool
	 */
	public function rollback() {
		if(!$this->link) return false;
		
		$result = $this->link->rollback();
		$this->link->autocommit(true);
		return $result;
	}
	
	/**
	 * 获取表结构
	 * @param string $table 表名
	 * @return array|false
	 */
	public function get_table_schema($table) {
		$sql = "DESCRIBE `$table`";
		$result = $this->query($sql);
		if(!$result) return false;
		
		$schema = [];
		while($row = $this->fetch_array($result)) {
			$schema[] = $row;
		}
		$this->free_result($result);
		
		return $schema;
	}
	
	/**
	 * 检查表是否存在
	 * @param string $table 表名
	 * @return bool
	 */
	public function table_exists($table) {
		$sql = "SHOW TABLES LIKE '$table'";
		$result = $this->query($sql);
		if(!$result) return false;
		
		$exists = $this->num_rows($result) > 0;
		$this->free_result($result);
		
		return $exists;
	}
	
	/**
	 * 获取连接统计信息
	 * @return array
	 */
	public function get_stats() {
		return [
			'queries' => count($this->sqls),
			'last_query' => end($this->sqls),
			'master_connected' => $this->wlink !== null,
			'slave_connected' => $this->rlink !== null,
			'last_errno' => $this->errno,
			'last_error' => $this->errstr
		];
	}
}

?>
