<?php

/*
* Copyright (C) 2024 boyou.com
* 基于 xiuno.com 进行现代化改造
*/

// 安全缩略，按照ID存储
/*
	$arr = image_safe_thumb('abc.jpg', 123, '.jpg', './upload/', 100, 100);
	array(
		'filesize'=>1234,
		'width'=>100,
		'height'=>100,
		'fileurl' => '001/0123/1233.jpg'
	);
*/

// 不包含 .
function image_ext($filename) {
	return strtolower(substr(strrchr($filename, '.'), 1));
}

// 获取图片信息
function image_info($filename) {
	if(!file_exists($filename)) return FALSE;
	
	$info = getimagesize($filename);
	if(!$info) return FALSE;
	
	return [
		'width' => $info[0],
		'height' => $info[1],
		'type' => $info[2],
		'mime' => $info['mime'],
		'size' => filesize($filename)
	];
}

// 检查是否为有效图片
function image_is_valid($filename) {
	$allowedTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP];
	$info = image_info($filename);
	return $info && in_array($info['type'], $allowedTypes);
}

// 创建图片资源
function image_create_from_file($filename) {
	$info = image_info($filename);
	if(!$info) return FALSE;
	
	switch($info['type']) {
		case IMAGETYPE_JPEG:
			return imagecreatefromjpeg($filename);
		case IMAGETYPE_PNG:
			return imagecreatefrompng($filename);
		case IMAGETYPE_GIF:
			return imagecreatefromgif($filename);
		case IMAGETYPE_WEBP:
			if(function_exists('imagecreatefromwebp')) {
				return imagecreatefromwebp($filename);
			}
			break;
	}
	
	return FALSE;
}

// 保存图片
function image_save($image, $filename, $quality = 90) {
	$ext = image_ext($filename);
	
	switch($ext) {
		case 'jpg':
		case 'jpeg':
			return imagejpeg($image, $filename, $quality);
		case 'png':
			// PNG质量范围是0-9，需要转换
			$pngQuality = 9 - floor($quality / 10);
			return imagepng($image, $filename, $pngQuality);
		case 'gif':
			return imagegif($image, $filename);
		case 'webp':
			if(function_exists('imagewebp')) {
				return imagewebp($image, $filename, $quality);
			}
			break;
	}
	
	return FALSE;
}

// 图片缩放
function image_resize($srcImage, $newWidth, $newHeight, $keepRatio = true) {
	$srcWidth = imagesx($srcImage);
	$srcHeight = imagesy($srcImage);
	
	if($keepRatio) {
		$ratio = min($newWidth / $srcWidth, $newHeight / $srcHeight);
		$newWidth = $srcWidth * $ratio;
		$newHeight = $srcHeight * $ratio;
	}
	
	$newImage = imagecreatetruecolor($newWidth, $newHeight);
	
	// 保持透明度
	imagealphablending($newImage, false);
	imagesavealpha($newImage, true);
	$transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
	imagefill($newImage, 0, 0, $transparent);
	
	imagecopyresampled(
		$newImage, $srcImage,
		0, 0, 0, 0,
		$newWidth, $newHeight,
		$srcWidth, $srcHeight
	);
	
	return $newImage;
}

// 图片裁剪
function image_crop($srcImage, $x, $y, $width, $height) {
	$newImage = imagecreatetruecolor($width, $height);
	
	// 保持透明度
	imagealphablending($newImage, false);
	imagesavealpha($newImage, true);
	$transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
	imagefill($newImage, 0, 0, $transparent);
	
	imagecopy($newImage, $srcImage, 0, 0, $x, $y, $width, $height);
	
	return $newImage;
}

// 添加水印
function image_watermark($srcImage, $watermarkFile, $position = 'bottom-right', $opacity = 50) {
	if(!file_exists($watermarkFile)) return $srcImage;
	
	$watermark = image_create_from_file($watermarkFile);
	if(!$watermark) return $srcImage;
	
	$srcWidth = imagesx($srcImage);
	$srcHeight = imagesy($srcImage);
	$wmWidth = imagesx($watermark);
	$wmHeight = imagesy($watermark);
	
	// 计算水印位置
	switch($position) {
		case 'top-left':
			$x = 10;
			$y = 10;
			break;
		case 'top-right':
			$x = $srcWidth - $wmWidth - 10;
			$y = 10;
			break;
		case 'bottom-left':
			$x = 10;
			$y = $srcHeight - $wmHeight - 10;
			break;
		case 'bottom-right':
		default:
			$x = $srcWidth - $wmWidth - 10;
			$y = $srcHeight - $wmHeight - 10;
			break;
		case 'center':
			$x = ($srcWidth - $wmWidth) / 2;
			$y = ($srcHeight - $wmHeight) / 2;
			break;
	}
	
	// 应用水印
	imagecopymerge($srcImage, $watermark, $x, $y, 0, 0, $wmWidth, $wmHeight, $opacity);
	imagedestroy($watermark);
	
	return $srcImage;
}

// 生成缩略图
function image_thumb($srcFile, $destFile, $maxWidth, $maxHeight, $quality = 90) {
	$srcImage = image_create_from_file($srcFile);
	if(!$srcImage) return FALSE;
	
	$thumbImage = image_resize($srcImage, $maxWidth, $maxHeight, true);
	$result = image_save($thumbImage, $destFile, $quality);
	
	imagedestroy($srcImage);
	imagedestroy($thumbImage);
	
	return $result;
}

// 安全的缩略图生成（按ID存储）
function image_safe_thumb($filename, $id, $ext, $uploadPath, $maxWidth, $maxHeight, $quality = 90) {
	if(!image_is_valid($filename)) {
		return FALSE;
	}
	
	// 生成存储路径
	$idStr = sprintf('%09d', $id);
	$dir1 = substr($idStr, 0, 3);
	$dir2 = substr($idStr, 3, 4);
	$fileName = substr($idStr, 7, 2) . $ext;
	
	$relativePath = $dir1 . '/' . $dir2 . '/' . $fileName;
	$fullDir = $uploadPath . $dir1 . '/' . $dir2;
	$fullPath = $uploadPath . $relativePath;
	
	// 创建目录
	if(!is_dir($fullDir)) {
		mkdir($fullDir, 0755, true);
	}
	
	// 生成缩略图
	if(image_thumb($filename, $fullPath, $maxWidth, $maxHeight, $quality)) {
		$info = image_info($fullPath);
		return [
			'filesize' => $info['size'],
			'width' => $info['width'],
			'height' => $info['height'],
			'fileurl' => $relativePath
		];
	}
	
	return FALSE;
}

// 图片格式转换
function image_convert($srcFile, $destFile, $quality = 90) {
	$srcImage = image_create_from_file($srcFile);
	if(!$srcImage) return FALSE;
	
	$result = image_save($srcImage, $destFile, $quality);
	imagedestroy($srcImage);
	
	return $result;
}

// 获取图片主色调
function image_dominant_color($filename) {
	$image = image_create_from_file($filename);
	if(!$image) return FALSE;
	
	$width = imagesx($image);
	$height = imagesy($image);
	
	// 缩小图片以提高性能
	$smallImage = image_resize($image, 50, 50, false);
	imagedestroy($image);
	
	$colors = [];
	for($x = 0; $x < 50; $x++) {
		for($y = 0; $y < 50; $y++) {
			$rgb = imagecolorat($smallImage, $x, $y);
			$r = ($rgb >> 16) & 0xFF;
			$g = ($rgb >> 8) & 0xFF;
			$b = $rgb & 0xFF;
			
			$key = sprintf('%02x%02x%02x', $r, $g, $b);
			$colors[$key] = ($colors[$key] ?? 0) + 1;
		}
	}
	
	imagedestroy($smallImage);
	
	// 找到最常见的颜色
	arsort($colors);
	$dominantColor = key($colors);
	
	return '#' . $dominantColor;
}

// 检查图片是否需要旋转（基于EXIF）
function image_auto_rotate($filename) {
	if(!function_exists('exif_read_data')) return FALSE;
	
	$exif = @exif_read_data($filename);
	if(!$exif || !isset($exif['Orientation'])) return FALSE;
	
	$image = image_create_from_file($filename);
	if(!$image) return FALSE;
	
	switch($exif['Orientation']) {
		case 3:
			$image = imagerotate($image, 180, 0);
			break;
		case 6:
			$image = imagerotate($image, -90, 0);
			break;
		case 8:
			$image = imagerotate($image, 90, 0);
			break;
		default:
			return $image;
	}
	
	return $image;
}

?>
