<?php

/*
	BoyouPHP 加密解密函数
	基于 XiunoPHP 进行现代化改造
	
	加载 boyou.so 能大大提高加密解密的速度，大约 250 倍，并且安全性会得到更大的提升。
	
	boyou.so 中包含了
	boyou_key()
	boyou_encrypt($data, $key)
	boyou_decrypt($data, $key)
	boyou_get_cpuid(0);
*/

// 获取配置文件中的 key，优先从扩展中获取（比较安全）。 
function xn_key($fromso = TRUE) {
	$conf = G('conf') ?: [];
	return ($fromso && function_exists('boyou_key')) ? boyou_key() : ($conf['auth_key'] ?? '');
}

// 安全的加密 key，过期时间 100 秒
// 临时使用，一般用作数据传输和校验
function xn_safe_key() {
	$conf = G('conf') ?: [];
	$longip = G('longip') ?: 0;
	$time = G('time') ?: time();
	$useragent = G('useragent') ?: '';
	
	$key = xn_key();
	$behind = intval(substr($time, -2, 2));
	
	// 如果最后2位大于90，则使用临时key
	if($behind > 90) {
		$key = md5($key . $longip . $useragent . intval($time / 100));
	}
	
	return $key;
}

// 生成随机密钥
function xn_generate_key($length = 32) {
	if(function_exists('random_bytes')) {
		return bin2hex(random_bytes($length / 2));
	} elseif(function_exists('openssl_random_pseudo_bytes')) {
		return bin2hex(openssl_random_pseudo_bytes($length / 2));
	} else {
		$chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$key = '';
		for($i = 0; $i < $length; $i++) {
			$key .= $chars[mt_rand(0, strlen($chars) - 1)];
		}
		return $key;
	}
}

// 加密函数
function xn_encrypt($data, $key = '') {
	if(empty($data)) return '';
	
	// 优先使用扩展
	if(function_exists('boyou_encrypt')) {
		return boyou_encrypt($data, $key);
	}
	
	// 使用内置加密
	if(empty($key)) {
		$key = xn_key();
	}
	
	if(empty($key)) {
		return base64_encode($data);
	}
	
	// 使用 AES-256-CBC 加密
	if(function_exists('openssl_encrypt')) {
		$iv = openssl_random_pseudo_bytes(16);
		$encrypted = openssl_encrypt($data, 'AES-256-CBC', hash('sha256', $key), 0, $iv);
		return base64_encode($iv . $encrypted);
	}
	
	// 回退到简单的XOR加密
	return base64_encode(xn_xor_encrypt($data, $key));
}

// 解密函数
function xn_decrypt($data, $key = '') {
	if(empty($data)) return '';
	
	// 优先使用扩展
	if(function_exists('boyou_decrypt')) {
		return boyou_decrypt($data, $key);
	}
	
	// 使用内置解密
	if(empty($key)) {
		$key = xn_key();
	}
	
	if(empty($key)) {
		return base64_decode($data);
	}
	
	$data = base64_decode($data);
	if($data === false) return '';
	
	// 使用 AES-256-CBC 解密
	if(function_exists('openssl_decrypt') && strlen($data) > 16) {
		$iv = substr($data, 0, 16);
		$encrypted = substr($data, 16);
		return openssl_decrypt($encrypted, 'AES-256-CBC', hash('sha256', $key), 0, $iv);
	}
	
	// 回退到简单的XOR解密
	return xn_xor_decrypt($data, $key);
}

// XOR 加密（简单实现）
function xn_xor_encrypt($data, $key) {
	$keyLen = strlen($key);
	$dataLen = strlen($data);
	$result = '';
	
	for($i = 0; $i < $dataLen; $i++) {
		$result .= chr(ord($data[$i]) ^ ord($key[$i % $keyLen]));
	}
	
	return $result;
}

// XOR 解密（简单实现）
function xn_xor_decrypt($data, $key) {
	return xn_xor_encrypt($data, $key); // XOR 加密和解密是相同的
}

// 密码哈希
function xn_password_hash($password, $salt = '') {
	if(empty($salt)) {
		$salt = xn_generate_key(16);
	}
	
	// 使用 PHP 内置的密码哈希
	if(function_exists('password_hash')) {
		return password_hash($password . $salt, PASSWORD_ARGON2ID) ?: password_hash($password . $salt, PASSWORD_DEFAULT);
	}
	
	// 回退到多重哈希
	$hash = $password . $salt;
	for($i = 0; $i < 1000; $i++) {
		$hash = hash('sha256', $hash);
	}
	
	return $salt . '$' . $hash;
}

// 密码验证
function xn_password_verify($password, $hash) {
	if(strpos($hash, '$') !== false) {
		// 新格式：salt$hash 或 PHP password_hash 格式
		if(function_exists('password_verify') && substr($hash, 0, 1) === '$') {
			return password_verify($password, $hash);
		}
		
		// 自定义格式
		$parts = explode('$', $hash, 2);
		if(count($parts) === 2) {
			$salt = $parts[0];
			$expectedHash = $parts[1];
			
			$testHash = $password . $salt;
			for($i = 0; $i < 1000; $i++) {
				$testHash = hash('sha256', $testHash);
			}
			
			return hash_equals($expectedHash, $testHash);
		}
	}
	
	// 旧格式兼容
	return hash_equals($hash, md5($password));
}

// 生成签名
function xn_sign($data, $key = '') {
	if(empty($key)) {
		$key = xn_key();
	}
	
	if(function_exists('hash_hmac')) {
		return hash_hmac('sha256', $data, $key);
	}
	
	return hash('sha256', $data . $key);
}

// 验证签名
function xn_verify_sign($data, $signature, $key = '') {
	$expectedSignature = xn_sign($data, $key);
	return hash_equals($expectedSignature, $signature);
}

// 生成令牌（带过期时间）
function xn_generate_token($data = '', $expire = 3600) {
	$time = time();
	$expireTime = $time + $expire;
	$payload = json_encode([
		'data' => $data,
		'exp' => $expireTime,
		'iat' => $time
	]);
	
	$token = base64_encode($payload);
	$signature = xn_sign($token);
	
	return $token . '.' . $signature;
}

// 验证令牌
function xn_verify_token($token) {
	$parts = explode('.', $token);
	if(count($parts) !== 2) {
		return false;
	}
	
	$payload = $parts[0];
	$signature = $parts[1];
	
	// 验证签名
	if(!xn_verify_sign($payload, $signature)) {
		return false;
	}
	
	// 解析载荷
	$data = json_decode(base64_decode($payload), true);
	if(!$data || !isset($data['exp'])) {
		return false;
	}
	
	// 检查过期时间
	if(time() > $data['exp']) {
		return false;
	}
	
	return $data;
}

// 安全的随机数生成
function xn_random_int($min = 0, $max = PHP_INT_MAX) {
	if(function_exists('random_int')) {
		return random_int($min, $max);
	} elseif(function_exists('mt_rand')) {
		return mt_rand($min, $max);
	} else {
		return rand($min, $max);
	}
}

// 安全的字符串比较
function xn_hash_equals($known, $user) {
	if(function_exists('hash_equals')) {
		return hash_equals($known, $user);
	}
	
	// 简单的时间安全比较
	if(strlen($known) !== strlen($user)) {
		return false;
	}
	
	$result = 0;
	for($i = 0; $i < strlen($known); $i++) {
		$result |= ord($known[$i]) ^ ord($user[$i]);
	}
	
	return $result === 0;
}

?>
