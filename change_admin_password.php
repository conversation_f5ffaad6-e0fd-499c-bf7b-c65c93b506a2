<?php

// 加载框架
define('DEBUG', 1);
define('APP_PATH', __DIR__.'/');
define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');

// 加载配置
$conf = include APP_PATH.'conf/conf.php';
$_SERVER['conf'] = $conf;

// 加载框架
include BOYOUPHP_PATH.'boyouphp.php';

// 加载模型
include APP_PATH.'model/plugin.func.php';
include _include(APP_PATH.'model.inc.php');

echo "=== Boyou BBS 6.1 管理员密码修改工具 ===\n\n";

class AdminPasswordChanger {
    private $pdo;
    
    public function __construct() {
        try {
            $this->pdo = new PDO('sqlite:data/boyou_bbs.db');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage() . "\n");
        }
    }
    
    public function changeAdminPassword() {
        echo "开始管理员密码修改流程...\n\n";
        
        // 1. 查找管理员账户
        $admin = $this->findAdminUser();
        if (!$admin) {
            echo "❌ 未找到管理员账户，创建新的管理员账户...\n";
            $this->createAdminUser();
            return;
        }
        
        echo "✓ 找到管理员账户: {$admin['username']} (UID: {$admin['uid']})\n";
        echo "  邮箱: {$admin['email']}\n";
        echo "  用户组: {$admin['gid']}\n";
        echo "  创建时间: " . date('Y-m-d H:i:s', $admin['create_date']) . "\n";
        echo "  最后登录: " . date('Y-m-d H:i:s', $admin['login_date']) . "\n\n";
        
        // 2. 生成新密码
        $newPassword = $this->generateSecurePassword();
        echo "✓ 生成新的安全密码: $newPassword\n\n";
        
        // 3. 更新密码
        $this->updatePassword($admin['uid'], $newPassword);
        
        // 4. 验证更新
        $this->verifyPasswordUpdate($admin['uid'], $newPassword);
        
        // 5. 显示登录信息
        $this->displayLoginInfo($admin['username'], $newPassword);
    }
    
    private function findAdminUser() {
        // 查找管理员用户 (gid = 1 表示管理员组)
        $stmt = $this->pdo->prepare("SELECT * FROM bbs_user WHERE gid = 1 ORDER BY uid ASC LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin) {
            // 如果没有找到gid=1的用户，查找uid=1的用户
            $stmt = $this->pdo->prepare("SELECT * FROM bbs_user WHERE uid = 1");
            $stmt->execute();
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return $admin;
    }
    
    private function createAdminUser() {
        $username = 'admin';
        $email = '<EMAIL>';
        $password = $this->generateSecurePassword();
        $time = time();
        
        echo "创建管理员账户信息:\n";
        echo "  用户名: $username\n";
        echo "  邮箱: $email\n";
        echo "  密码: $password\n\n";
        
        // 使用现代密码哈希
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO bbs_user (username, email, password, salt, gid, create_date, create_ip, login_date, login_ip, logins) 
                VALUES (?, ?, ?, '', 1, ?, 0, ?, 0, 1)";
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$username, $email, $password_hash, $time, $time]);
            
            if ($result) {
                echo "✓ 管理员账户创建成功！\n\n";
                $this->displayLoginInfo($username, $password);
            } else {
                echo "❌ 管理员账户创建失败\n";
            }
        } catch (PDOException $e) {
            echo "❌ 创建管理员账户时发生错误: " . $e->getMessage() . "\n";
        }
    }
    
    private function generateSecurePassword($length = 12) {
        // 生成包含大小写字母、数字和特殊字符的安全密码
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '0123456789';
        $special = '!@#$%^&*';
        
        $password = '';
        
        // 确保至少包含每种类型的字符
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $special[random_int(0, strlen($special) - 1)];
        
        // 填充剩余长度
        $allChars = $lowercase . $uppercase . $numbers . $special;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }
        
        // 打乱字符顺序
        return str_shuffle($password);
    }
    
    private function updatePassword($uid, $newPassword) {
        echo "正在更新密码...\n";
        
        // 使用现代密码哈希
        $password_hash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        try {
            $stmt = $this->pdo->prepare("UPDATE bbs_user SET password = ?, salt = '' WHERE uid = ?");
            $result = $stmt->execute([$password_hash, $uid]);
            
            if ($result) {
                echo "✓ 密码更新成功\n";
            } else {
                echo "❌ 密码更新失败\n";
            }
        } catch (PDOException $e) {
            echo "❌ 更新密码时发生错误: " . $e->getMessage() . "\n";
        }
    }
    
    private function verifyPasswordUpdate($uid, $password) {
        echo "验证密码更新...\n";
        
        try {
            $stmt = $this->pdo->prepare("SELECT password FROM bbs_user WHERE uid = ?");
            $stmt->execute([$uid]);
            $hash = $stmt->fetchColumn();
            
            if ($hash && password_verify($password, $hash)) {
                echo "✓ 密码验证成功，更新已生效\n\n";
            } else {
                echo "❌ 密码验证失败，请检查更新是否成功\n\n";
            }
        } catch (PDOException $e) {
            echo "❌ 验证密码时发生错误: " . $e->getMessage() . "\n\n";
        }
    }
    
    private function displayLoginInfo($username, $password) {
        echo "=== 管理员登录信息 ===\n";
        echo "🌐 网站地址: http://localhost:8000/\n";
        echo "🔧 管理后台: http://localhost:8000/admin/\n";
        echo "👤 用户名: $username\n";
        echo "🔑 密码: $password\n\n";
        
        echo "⚠️  重要提醒:\n";
        echo "1. 请立即登录并修改密码\n";
        echo "2. 请妥善保管新密码\n";
        echo "3. 建议启用两步验证（如果支持）\n";
        echo "4. 定期更换密码\n";
        echo "5. 不要在不安全的网络环境下登录\n\n";
        
        // 保存登录信息到文件
        $loginInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'username' => $username,
            'password' => $password,
            'frontend_url' => 'http://localhost:8000/',
            'admin_url' => 'http://localhost:8000/admin/',
            'notes' => '请立即登录并修改密码'
        ];
        
        file_put_contents('admin_login_info.json', json_encode($loginInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 登录信息已保存到: admin_login_info.json\n";
        echo "   (请在使用后删除此文件以确保安全)\n\n";
    }
    
    public function listAllUsers() {
        echo "=== 系统用户列表 ===\n";
        
        try {
            $stmt = $this->pdo->query("SELECT uid, username, email, gid, create_date, login_date FROM bbs_user ORDER BY uid");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($users)) {
                echo "系统中没有用户\n";
                return;
            }
            
            foreach ($users as $user) {
                $userType = $user['gid'] == 1 ? '管理员' : '普通用户';
                echo "UID: {$user['uid']} | 用户名: {$user['username']} | 邮箱: {$user['email']} | 类型: $userType\n";
                echo "  创建时间: " . date('Y-m-d H:i:s', $user['create_date']) . "\n";
                echo "  最后登录: " . date('Y-m-d H:i:s', $user['login_date']) . "\n\n";
            }
        } catch (PDOException $e) {
            echo "❌ 获取用户列表失败: " . $e->getMessage() . "\n";
        }
    }
    
    public function checkPasswordSecurity() {
        echo "=== 密码安全检查 ===\n";
        
        try {
            $stmt = $this->pdo->query("SELECT uid, username, password FROM bbs_user");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $weakPasswords = 0;
            $modernHashes = 0;
            
            foreach ($users as $user) {
                if (strlen($user['password']) == 32 && ctype_xdigit($user['password'])) {
                    echo "⚠️  用户 {$user['username']} 使用弱密码哈希 (MD5)\n";
                    $weakPasswords++;
                } elseif (strpos($user['password'], '$') === 0) {
                    echo "✓ 用户 {$user['username']} 使用现代密码哈希\n";
                    $modernHashes++;
                } else {
                    echo "❓ 用户 {$user['username']} 密码格式未知\n";
                }
            }
            
            echo "\n密码安全统计:\n";
            echo "现代哈希: $modernHashes 个\n";
            echo "弱哈希: $weakPasswords 个\n";
            
            if ($weakPasswords > 0) {
                echo "\n建议: 升级弱密码哈希的用户密码\n";
            }
            
        } catch (PDOException $e) {
            echo "❌ 检查密码安全时发生错误: " . $e->getMessage() . "\n";
        }
    }
}

// 主程序
$changer = new AdminPasswordChanger();

// 显示菜单
echo "请选择操作:\n";
echo "1. 修改管理员密码\n";
echo "2. 查看所有用户\n";
echo "3. 检查密码安全\n";
echo "4. 退出\n\n";

// 在脚本模式下自动执行密码修改
if (php_sapi_name() === 'cli') {
    echo "自动执行管理员密码修改...\n\n";
    $changer->changeAdminPassword();
} else {
    echo "请在命令行模式下运行此脚本\n";
}

echo "=== 管理员密码修改完成 ===\n";

?>
