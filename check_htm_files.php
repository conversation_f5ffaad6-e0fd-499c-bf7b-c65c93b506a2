<?php

echo "=== HTM文件完整性检查 ===\n\n";

$htmFiles = [];
$issues = [];

// 递归查找所有.htm文件
function findHtmFiles($dir) {
    global $htmFiles;
    
    if (!is_dir($dir)) return;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'htm') {
            $htmFiles[] = $file->getPathname();
        }
    }
}

// 检查HTM文件
function checkHtmFile($file) {
    global $issues;
    
    if (!file_exists($file) || !is_readable($file)) {
        $issues[] = [
            'file' => $file,
            'type' => 'file_access',
            'message' => '文件不存在或不可读',
            'severity' => 'high'
        ];
        return;
    }
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    // 检查文件编码
    if (!mb_check_encoding($content, 'UTF-8')) {
        $issues[] = [
            'file' => $file,
            'type' => 'encoding',
            'message' => '文件编码不是UTF-8',
            'severity' => 'medium'
        ];
    }
    
    // 检查基本HTML结构
    $hasDoctype = false;
    $hasHtmlTag = false;
    $hasHeadTag = false;
    $hasBodyTag = false;
    $phpIncludeCount = 0;
    
    foreach ($lines as $lineNumber => $line) {
        $trimmedLine = trim($line);
        
        // 检查DOCTYPE
        if (stripos($trimmedLine, '<!DOCTYPE') !== false) {
            $hasDoctype = true;
        }
        
        // 检查HTML标签
        if (stripos($trimmedLine, '<html') !== false) {
            $hasHtmlTag = true;
        }
        
        // 检查HEAD标签
        if (stripos($trimmedLine, '<head') !== false) {
            $hasHeadTag = true;
        }
        
        // 检查BODY标签
        if (stripos($trimmedLine, '<body') !== false) {
            $hasBodyTag = true;
        }
        
        // 检查PHP include
        if (preg_match('/include.*?\.htm/', $line)) {
            $phpIncludeCount++;
        }
        
        // 检查未闭合的PHP标签
        if (preg_match('/\<\?php/', $line) && !preg_match('/\?\>/', $line)) {
            // 这是正常的，PHP标签可以不闭合
        }
        
        // 检查可能的语法错误
        if (preg_match('/\<\?php.*?\?\>.*?\<\?php/', $line)) {
            $issues[] = [
                'file' => $file,
                'line' => $lineNumber + 1,
                'type' => 'php_syntax',
                'message' => '同一行有多个PHP标签，可能有语法问题',
                'severity' => 'medium'
            ];
        }
        
        // 检查未转义的输出
        if (preg_match('/echo\s+\$[^;]*;/', $line) && !preg_match('/htmlspecialchars|strip_tags/', $line)) {
            $issues[] = [
                'file' => $file,
                'line' => $lineNumber + 1,
                'type' => 'xss_risk',
                'message' => '可能存在XSS风险：直接输出变量未转义',
                'severity' => 'medium'
            ];
        }
        
        // 检查xiuno相关的硬编码链接
        if (preg_match('/xiuno\.com|bbs\.xiuno/', $line)) {
            $issues[] = [
                'file' => $file,
                'line' => $lineNumber + 1,
                'type' => 'hardcoded_link',
                'message' => '发现xiuno.com硬编码链接',
                'severity' => 'low'
            ];
        }
    }
    
    // 检查是否是完整的HTML文档还是模板片段
    $isTemplate = $phpIncludeCount > 0 || !$hasDoctype;
    
    if (!$isTemplate) {
        // 完整HTML文档的检查
        if (!$hasDoctype) {
            $issues[] = [
                'file' => $file,
                'type' => 'html_structure',
                'message' => '缺少DOCTYPE声明',
                'severity' => 'medium'
            ];
        }
        
        if (!$hasHtmlTag) {
            $issues[] = [
                'file' => $file,
                'type' => 'html_structure',
                'message' => '缺少HTML标签',
                'severity' => 'medium'
            ];
        }
        
        if (!$hasHeadTag) {
            $issues[] = [
                'file' => $file,
                'type' => 'html_structure',
                'message' => '缺少HEAD标签',
                'severity' => 'medium'
            ];
        }
        
        if (!$hasBodyTag) {
            $issues[] = [
                'file' => $file,
                'type' => 'html_structure',
                'message' => '缺少BODY标签',
                'severity' => 'medium'
            ];
        }
    }
}

// 查找所有HTM文件
$directories = ['view', 'admin', 'install', 'tmp'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        findHtmFiles($dir);
    }
}

echo "找到 " . count($htmFiles) . " 个HTM文件\n\n";

// 检查每个文件
foreach ($htmFiles as $file) {
    checkHtmFile($file);
}

// 统计结果
$severityCount = ['high' => 0, 'medium' => 0, 'low' => 0];
$typeCount = [];

foreach ($issues as $issue) {
    $severityCount[$issue['severity']]++;
    $typeCount[$issue['type']] = ($typeCount[$issue['type']] ?? 0) + 1;
}

echo "检查完成！\n";
echo "发现问题数: " . count($issues) . "\n\n";

echo "严重程度分布:\n";
echo "  高危: " . $severityCount['high'] . "\n";
echo "  中等: " . $severityCount['medium'] . "\n";
echo "  低危: " . $severityCount['low'] . "\n\n";

if (!empty($typeCount)) {
    echo "问题类型分布:\n";
    foreach ($typeCount as $type => $count) {
        echo "  $type: $count\n";
    }
    echo "\n";
}

if (!empty($issues)) {
    echo "详细问题列表:\n";
    echo str_repeat("=", 80) . "\n";
    
    foreach ($issues as $issue) {
        echo sprintf("[%s] %s\n", 
            strtoupper($issue['severity']), 
            $issue['file']
        );
        if (isset($issue['line'])) {
            echo "  行号: " . $issue['line'] . "\n";
        }
        echo "  类型: " . $issue['type'] . "\n";
        echo "  描述: " . $issue['message'] . "\n";
        echo str_repeat("-", 40) . "\n";
    }
}

echo "\n=== HTM文件检查完成 ===\n";

?>
