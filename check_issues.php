<?php

echo "=== Boyou BBS 代码质量检查 ===\n\n";

// 检查已弃用函数的使用
$deprecatedFunctions = [
    'mysql_connect', 'mysql_query', 'mysql_fetch_array', 'mysql_fetch_assoc',
    'mysql_num_rows', 'mysql_insert_id', 'mysql_affected_rows', 'mysql_close',
    'ereg', 'eregi', 'split', 'each', 'create_function'
];

$issues = [];
$checkedFiles = 0;

function checkFile($file) {
    global $deprecatedFunctions, $issues, $checkedFiles;
    
    if (!file_exists($file) || !is_readable($file)) {
        return;
    }
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    $checkedFiles++;
    
    foreach ($lines as $lineNumber => $line) {
        // 检查已弃用函数
        foreach ($deprecatedFunctions as $func) {
            if (preg_match('/\b' . preg_quote($func) . '\s*\(/', $line)) {
                $issues[] = [
                    'file' => $file,
                    'line' => $lineNumber + 1,
                    'type' => 'deprecated_function',
                    'message' => "使用了已弃用的函数: $func",
                    'severity' => 'high'
                ];
            }
        }
        
        // 检查旧数组语法
        if (preg_match('/\barray\s*\(/', $line)) {
            $issues[] = [
                'file' => $file,
                'line' => $lineNumber + 1,
                'type' => 'old_array_syntax',
                'message' => '使用了旧式数组语法 array()，建议使用 []',
                'severity' => 'low'
            ];
        }
        
        // 检查可能的SQL注入
        if (preg_match('/\$_(?:GET|POST|REQUEST)\[.*?\].*?(?:SELECT|INSERT|UPDATE|DELETE)/i', $line)) {
            $issues[] = [
                'file' => $file,
                'line' => $lineNumber + 1,
                'type' => 'sql_injection_risk',
                'message' => '可能存在SQL注入风险：直接使用用户输入构建SQL',
                'severity' => 'high'
            ];
        }
    }
}

// 检查主要目录
$directories = ['model', 'route', 'admin', 'xiunophp', 'install'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                checkFile($file->getPathname());
            }
        }
    }
}

// 检查根目录的PHP文件
$rootFiles = glob('*.php');
foreach ($rootFiles as $file) {
    checkFile($file);
}

// 统计结果
$severityCount = ['high' => 0, 'medium' => 0, 'low' => 0];
$typeCount = [];

foreach ($issues as $issue) {
    $severityCount[$issue['severity']]++;
    $typeCount[$issue['type']] = ($typeCount[$issue['type']] ?? 0) + 1;
}

echo "检查文件数: $checkedFiles\n";
echo "发现问题数: " . count($issues) . "\n\n";

echo "严重程度分布:\n";
echo "  高危: " . $severityCount['high'] . "\n";
echo "  中等: " . $severityCount['medium'] . "\n";
echo "  低危: " . $severityCount['low'] . "\n\n";

echo "问题类型分布:\n";
foreach ($typeCount as $type => $count) {
    echo "  $type: $count\n";
}

if (!empty($issues)) {
    echo "\n详细问题列表:\n";
    echo str_repeat("=", 80) . "\n";
    
    // 按严重程度排序
    usort($issues, function($a, $b) {
        $severityOrder = ['high' => 3, 'medium' => 2, 'low' => 1];
        return $severityOrder[$b['severity']] - $severityOrder[$a['severity']];
    });
    
    foreach ($issues as $issue) {
        echo sprintf("[%s] %s:%d\n", 
            strtoupper($issue['severity']), 
            $issue['file'], 
            $issue['line']
        );
        echo "  类型: " . $issue['type'] . "\n";
        echo "  描述: " . $issue['message'] . "\n";
        echo str_repeat("-", 40) . "\n";
    }
}

echo "\n=== 检查完成 ===\n";

?>
