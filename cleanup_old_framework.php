<?php

echo "=== 清理工作：删除 xiunophp 目录 ===\n\n";

// 1. 最终确认检查
echo "1. 最终确认检查...\n";

// 检查boyouphp目录是否完整
if (!is_dir('boyouphp')) {
    echo "✗ boyouphp 目录不存在，无法进行清理\n";
    exit(1);
}

$boyouFiles = scandir('boyouphp');
$boyouPhpFiles = array_filter($boyouFiles, function($f) { 
    return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
});

echo "boyouphp 目录包含 " . count($boyouPhpFiles) . " 个PHP文件\n";

if (count($boyouPhpFiles) < 10) {
    echo "⚠️  boyouphp 目录文件数量不足，建议不要删除 xiunophp\n";
    exit(1);
}

// 检查关键文件
$keyFiles = ['boyouphp.php', 'boyouphp.min.php', 'db.func.php', 'misc.func.php'];
$missingKeyFiles = [];

foreach ($keyFiles as $file) {
    if (!file_exists("boyouphp/$file")) {
        $missingKeyFiles[] = $file;
    }
}

if (!empty($missingKeyFiles)) {
    echo "⚠️  关键文件缺失: " . implode(', ', $missingKeyFiles) . "\n";
    echo "建议不要删除 xiunophp 目录\n";
    exit(1);
}

echo "✓ boyouphp 目录完整性检查通过\n";

// 2. 检查是否还有对xiunophp的引用
echo "\n2. 检查是否还有对xiunophp的引用...\n";

$directories = ['.', 'admin', 'install', 'plugin', 'tmp', 'tool'];
$xiunophpReferences = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if (!$file->isFile()) continue;
        
        $extension = strtolower($file->getExtension());
        if (!in_array($extension, ['php', 'htm', 'html', 'js'])) continue;
        
        $filePath = $file->getPathname();
        
        // 跳过xiunophp和boyouphp目录
        if (strpos($filePath, 'xiunophp/') !== false || 
            strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        if (strpos($content, 'xiunophp') !== false) {
            $xiunophpReferences[] = $filePath;
        }
    }
}

if (!empty($xiunophpReferences)) {
    echo "⚠️  发现以下文件仍引用 xiunophp:\n";
    foreach ($xiunophpReferences as $file) {
        echo "  $file\n";
    }
    echo "建议先修复这些引用再删除 xiunophp 目录\n";
    exit(1);
}

echo "✓ 没有发现对 xiunophp 的引用\n";

// 3. 测试框架是否正常工作
echo "\n3. 测试框架是否正常工作...\n";

try {
    // 设置必要的常量
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', './');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', './boyouphp/');
    
    // 尝试包含框架
    ob_start();
    include_once 'boyouphp/boyouphp.php';
    $output = ob_get_clean();
    
    if (defined('BOYOUPHP_VERSION')) {
        echo "✓ BoyouPHP 框架正常工作，版本: " . BOYOUPHP_VERSION . "\n";
    } else {
        echo "⚠️  框架版本常量未定义\n";
        exit(1);
    }
    
    // 测试基本函数
    if (function_exists('G')) {
        G('test', 'cleanup_test');
        if (G('test') === 'cleanup_test') {
            echo "✓ 核心函数正常工作\n";
        } else {
            echo "⚠️  核心函数工作异常\n";
            exit(1);
        }
    } else {
        echo "⚠️  核心函数不存在\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "✗ 框架测试失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. 备份xiunophp目录（可选）
echo "\n4. 创建备份...\n";

$backupDir = 'xiunophp_backup_' . date('Y-m-d_H-i-s');
if (!is_dir('xiunophp')) {
    echo "! xiunophp 目录不存在，无需备份\n";
} else {
    echo "创建备份目录: $backupDir\n";
    
    // 使用系统命令复制目录
    $command = "cp -r xiunophp $backupDir";
    $result = exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && is_dir($backupDir)) {
        echo "✓ 备份创建成功: $backupDir\n";
    } else {
        echo "⚠️  备份创建失败，建议手动备份\n";
        echo "建议执行: cp -r xiunophp xiunophp_backup\n";
        exit(1);
    }
}

// 5. 删除xiunophp目录
echo "\n5. 删除 xiunophp 目录...\n";

if (!is_dir('xiunophp')) {
    echo "! xiunophp 目录不存在，无需删除\n";
} else {
    // 递归删除目录
    function deleteDirectory($dir) {
        if (!is_dir($dir)) return false;
        
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
    
    if (deleteDirectory('xiunophp')) {
        echo "✓ xiunophp 目录删除成功\n";
    } else {
        echo "✗ xiunophp 目录删除失败\n";
        exit(1);
    }
}

// 6. 最终验证
echo "\n6. 最终验证...\n";

if (is_dir('xiunophp')) {
    echo "✗ xiunophp 目录仍然存在\n";
} else {
    echo "✓ xiunophp 目录已成功删除\n";
}

if (is_dir('boyouphp')) {
    echo "✓ boyouphp 目录正常存在\n";
} else {
    echo "✗ boyouphp 目录不存在！\n";
}

if (is_dir($backupDir)) {
    echo "✓ 备份目录存在: $backupDir\n";
}

echo "\n=== 清理工作完成 ===\n";

echo "\n📋 清理总结:\n";
echo "  ✅ xiunophp 目录已删除\n";
echo "  ✅ boyouphp 目录正常工作\n";
echo "  ✅ 备份已创建: $backupDir\n";
echo "  ✅ 框架功能正常\n";

echo "\n🎉 清理工作成功完成！\n";
echo "\n💡 提示:\n";
echo "  - 如果需要恢复，可以使用备份目录: $backupDir\n";
echo "  - 建议测试网站所有功能确保正常\n";
echo "  - 确认无误后可以删除备份目录\n";

?>
