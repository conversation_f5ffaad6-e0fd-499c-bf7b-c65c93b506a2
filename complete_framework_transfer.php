<?php

echo "=== 完整转移 xiunophp 到 boyouphp ===\n\n";

$sourceDir = 'xiunophp';
$targetDir = 'boyouphp';

// 确保目标目录存在
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0755, true);
    echo "✓ 创建目标目录: $targetDir\n";
}

// 获取源目录中的所有文件
if (!is_dir($sourceDir)) {
    echo "✗ 源目录不存在: $sourceDir\n";
    exit(1);
}

echo "开始转移文件...\n\n";

$files = scandir($sourceDir);
$transferredFiles = [];
$skippedFiles = [];
$renamedFiles = [];
$errorFiles = [];

foreach ($files as $file) {
    if ($file === '.' || $file === '..') continue;
    
    $sourcePath = $sourceDir . '/' . $file;
    
    // 跳过目录
    if (is_dir($sourcePath)) {
        echo "跳过目录: $file\n";
        continue;
    }
    
    // 确定目标文件名
    $targetFileName = $file;
    
    // 重命名规则
    if ($file === 'xiunophp.php') {
        $targetFileName = 'boyouphp.php';
        $renamedFiles[] = "$file → $targetFileName";
    } elseif ($file === 'xiunophp.min.php') {
        $targetFileName = 'boyouphp.min.php';
        $renamedFiles[] = "$file → $targetFileName";
    }
    
    $targetPath = $targetDir . '/' . $targetFileName;
    
    // 检查目标文件是否已存在
    if (file_exists($targetPath)) {
        // 比较文件大小
        $sourceSize = filesize($sourcePath);
        $targetSize = filesize($targetPath);
        
        if ($sourceSize === $targetSize) {
            echo "跳过相同文件: $targetFileName\n";
            $skippedFiles[] = $targetFileName;
            continue;
        } else {
            echo "覆盖不同文件: $targetFileName (源:{$sourceSize}字节 vs 目标:{$targetSize}字节)\n";
        }
    }
    
    // 复制文件
    if (copy($sourcePath, $targetPath)) {
        echo "✓ 转移: $file → $targetFileName\n";
        $transferredFiles[] = $targetFileName;
        
        // 对于主框架文件，需要更新内容
        if ($targetFileName === 'boyouphp.php' || $targetFileName === 'boyouphp.min.php') {
            $content = file_get_contents($targetPath);
            
            // 更新注释和引用
            $content = str_replace('XiunoPHP 4.0', 'BoyouPHP 6.1', $content);
            $content = str_replace('xiunophp', 'boyouphp', $content);
            $content = str_replace('XIUNOPHP', 'BOYOUPHP', $content);
            
            // 确保版本常量存在
            if (strpos($content, "define('BOYOUPHP_VERSION'") === false) {
                $content .= "\n// 版本信息\ndefine('BOYOUPHP_VERSION', '6.1');\ndefine('BOYOU_BBS_VERSION', '6.1');\n";
            }
            
            file_put_contents($targetPath, $content);
            echo "  ✓ 更新文件内容: $targetFileName\n";
        }
        
    } else {
        echo "✗ 转移失败: $file\n";
        $errorFiles[] = $file;
    }
}

echo "\n=== 转移结果 ===\n";
echo "成功转移: " . count($transferredFiles) . " 个文件\n";
echo "跳过文件: " . count($skippedFiles) . " 个文件\n";
echo "失败文件: " . count($errorFiles) . " 个文件\n";

if (!empty($renamedFiles)) {
    echo "\n重命名文件:\n";
    foreach ($renamedFiles as $rename) {
        echo "  $rename\n";
    }
}

if (!empty($transferredFiles)) {
    echo "\n转移的文件:\n";
    foreach ($transferredFiles as $file) {
        echo "  $file\n";
    }
}

if (!empty($errorFiles)) {
    echo "\n失败的文件:\n";
    foreach ($errorFiles as $file) {
        echo "  $file\n";
    }
}

// 验证关键文件
echo "\n=== 验证关键文件 ===\n";
$keyFiles = [
    'boyouphp.php',
    'boyouphp.min.php',
    'db.func.php',
    'cache.func.php',
    'misc.func.php',
    'array.func.php',
    'image.func.php',
    'xn_encrypt.func.php',
    'db_mysql.class.php',
    'db_pdo_mysql.class.php',
    'db_pdo_sqlite.class.php',
    'cache_redis.class.php',
    'cache_apc.class.php',
    'cache_memcached.class.php',
    'cache_mysql.class.php',
    'cache_xcache.class.php',
    'cache_yac.class.php'
];

$missingFiles = [];
$existingFiles = [];

foreach ($keyFiles as $file) {
    $path = $targetDir . '/' . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✓ $file (" . number_format($size) . " 字节)\n";
        $existingFiles[] = $file;
    } else {
        echo "✗ $file 缺失\n";
        $missingFiles[] = $file;
    }
}

echo "\n=== 统计信息 ===\n";
echo "关键文件总数: " . count($keyFiles) . "\n";
echo "存在文件: " . count($existingFiles) . "\n";
echo "缺失文件: " . count($missingFiles) . "\n";

$completionRate = count($existingFiles) / count($keyFiles) * 100;
echo "完成率: " . round($completionRate, 1) . "%\n";

if ($completionRate >= 90) {
    echo "\n🎉 框架转移基本完成！\n";
} elseif ($completionRate >= 70) {
    echo "\n⚠️  框架转移大部分完成，还有一些文件需要处理\n";
} else {
    echo "\n❌ 框架转移未完成，需要进一步处理\n";
}

if (!empty($missingFiles)) {
    echo "\n缺失的关键文件:\n";
    foreach ($missingFiles as $file) {
        echo "  - $file\n";
    }
}

echo "\n=== 转移完成 ===\n";

?>
