<?php

echo "=== 完成 xiunophp 到 boyouphp 的迁移 ===\n\n";

// 1. 重命名 tmp 目录中的文件
echo "1. 重命名 tmp 目录中的文件...\n";
$oldTmpFile = 'tmp/xiunophp_xn_send_mail.func.php';
$newTmpFile = 'tmp/boyouphp_xn_send_mail.func.php';

if (file_exists($oldTmpFile) && !file_exists($newTmpFile)) {
    if (copy($oldTmpFile, $newTmpFile)) {
        echo "✓ 重命名: $oldTmpFile → $newTmpFile\n";
        unlink($oldTmpFile);
    } else {
        echo "✗ 重命名失败: $oldTmpFile\n";
    }
} elseif (file_exists($newTmpFile)) {
    echo "✓ $newTmpFile 已存在\n";
}

// 2. 确保 boyou.js 存在且内容正确
echo "\n2. 确保 boyou.js 文件...\n";
if (!file_exists('view/js/boyou.js') && file_exists('view/js/xiuno.js')) {
    $content = file_get_contents('view/js/xiuno.js');
    if (file_put_contents('view/js/boyou.js', $content)) {
        echo "✓ 创建 boyou.js 成功\n";
    } else {
        echo "✗ 创建 boyou.js 失败\n";
    }
} elseif (file_exists('view/js/boyou.js')) {
    echo "✓ boyou.js 已存在\n";
}

// 3. 检查并复制缺失的 boyouphp 文件
echo "\n3. 检查 boyouphp 目录完整性...\n";
$requiredFiles = [
    'boyouphp.php',
    'boyouphp.min.php',
    'db.func.php',
    'cache.func.php', 
    'misc.func.php',
    'array.func.php',
    'image.func.php',
    'xn_encrypt.func.php'
];

$missingCount = 0;
foreach ($requiredFiles as $file) {
    $boyouPath = "boyouphp/$file";
    $xiunoPath = "xiunophp/$file";
    
    if (!file_exists($boyouPath)) {
        if (file_exists($xiunoPath)) {
            $content = file_get_contents($xiunoPath);
            
            // 对于主文件，需要重命名
            if ($file === 'boyouphp.php' && file_exists('xiunophp/xiunophp.php')) {
                $content = file_get_contents('xiunophp/xiunophp.php');
            } elseif ($file === 'boyouphp.min.php' && file_exists('xiunophp/xiunophp.min.php')) {
                $content = file_get_contents('xiunophp/xiunophp.min.php');
            }
            
            if (file_put_contents($boyouPath, $content)) {
                echo "✓ 复制: $file\n";
            } else {
                echo "✗ 复制失败: $file\n";
                $missingCount++;
            }
        } else {
            echo "⚠️  源文件不存在: $xiunoPath\n";
            $missingCount++;
        }
    } else {
        echo "✓ $file 已存在\n";
    }
}

// 4. 更新 boyouphp.php 中的注释和版本信息
echo "\n4. 更新 boyouphp.php 文件...\n";
if (file_exists('boyouphp/boyouphp.php')) {
    $content = file_get_contents('boyouphp/boyouphp.php');
    
    // 更新注释
    $content = str_replace('XiunoPHP 4.0', 'BoyouPHP 6.1', $content);
    $content = str_replace('xiunophp', 'boyouphp', $content);
    $content = str_replace('XIUNOPHP', 'BOYOUPHP', $content);
    
    // 确保版本常量正确
    if (strpos($content, "define('BOYOUPHP_VERSION', '6.1')") === false) {
        $content = str_replace(
            "define('BOYOUPHP_VERSION', '6.1')",
            "define('BOYOUPHP_VERSION', '6.1')",
            $content
        );
        if (strpos($content, "define('BOYOUPHP_VERSION'") === false) {
            $content .= "\n// 版本信息\ndefine('BOYOUPHP_VERSION', '6.1');\ndefine('BOYOU_BBS_VERSION', '6.1');\n";
        }
    }
    
    if (file_put_contents('boyouphp/boyouphp.php', $content)) {
        echo "✓ 更新 boyouphp.php 成功\n";
    } else {
        echo "✗ 更新 boyouphp.php 失败\n";
    }
}

// 5. 生成迁移完成报告
echo "\n=== 迁移完成报告 ===\n";

$report = [
    'boyouphp_directory' => is_dir('boyouphp') ? '存在' : '不存在',
    'xiunophp_directory' => is_dir('xiunophp') ? '存在' : '不存在',
    'boyou_js_file' => file_exists('view/js/boyou.js') ? '存在' : '不存在',
    'xiuno_js_file' => file_exists('view/js/xiuno.js') ? '存在' : '不存在',
    'missing_files_count' => $missingCount,
    'migration_status' => $missingCount === 0 ? '完成' : '部分完成'
];

foreach ($report as $key => $value) {
    echo "$key: $value\n";
}

// 6. 最终建议
echo "\n=== 最终建议 ===\n";

if ($report['migration_status'] === '完成') {
    echo "🎉 迁移已完成！\n\n";
    echo "下一步建议:\n";
    echo "1. 测试网站功能是否正常\n";
    echo "2. 确认所有页面都正确显示 'Boyou BBS'\n";
    echo "3. 检查 JavaScript 功能是否正常\n";
    echo "4. 备份后可考虑删除 xiunophp 目录\n";
    echo "5. 备份后可考虑删除 view/js/xiuno.js\n";
} else {
    echo "⚠️  迁移部分完成，还有 $missingCount 个文件需要处理\n";
    echo "请检查上述输出中的错误信息\n";
}

echo "\n=== 迁移脚本执行完成 ===\n";

?>
