<?php
/**
 * Boyou BBS 6.1 SMTP邮件配置
 * 配置SMTP服务器信息以支持邮件发送功能
 */

return [
    // SMTP服务器配置
    'smtp_on' => false,                    // 是否启用SMTP (设为true启用)
    'smtp_server' => 'smtp.gmail.com',     // SMTP服务器地址
    'smtp_port' => 587,                    // SMTP端口 (587=TLS, 465=SSL, 25=无加密)
    'smtp_ssl' => 'tls',                   // 加密方式: tls, ssl, 或 false
    'smtp_username' => '',                 // SMTP用户名 (通常是邮箱地址)
    'smtp_password' => '',                 // SMTP密码 (应用专用密码)

    // 发件人信息
    'mail_from' => '<EMAIL>',    // 发件人邮箱
    'mail_from_name' => 'Boyou BBS',       // 发件人名称

    // 邮件模板配置
    'mail_charset' => 'UTF-8',             // 邮件字符集
    'mail_html' => true,                   // 是否发送HTML邮件

    // 常用SMTP服务器配置示例:

    // Gmail配置示例:
    'gmail' => [
        'smtp_server' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_ssl' => 'tls',
        'note' => '需要开启两步验证并使用应用专用密码'
    ],

    // QQ邮箱配置示例:
    'qq' => [
        'smtp_server' => 'smtp.qq.com',
        'smtp_port' => 587,
        'smtp_ssl' => 'tls',
        'note' => '需要开启SMTP服务并获取授权码'
    ],

    // 163邮箱配置示例:
    '163' => [
        'smtp_server' => 'smtp.163.com',
        'smtp_port' => 587,
        'smtp_ssl' => 'tls',
        'note' => '需要开启SMTP服务并设置客户端授权密码'
    ],

    // 阿里云邮件推送配置示例:
    'aliyun' => [
        'smtp_server' => 'smtpdm.aliyun.com',
        'smtp_port' => 587,
        'smtp_ssl' => 'tls',
        'note' => '需要在阿里云控制台配置邮件推送服务'
    ],

    // 腾讯企业邮箱配置示例:
    'tencent' => [
        'smtp_server' => 'smtp.exmail.qq.com',
        'smtp_port' => 587,
        'smtp_ssl' => 'tls',
        'note' => '企业邮箱配置'
    ]
];

?>
