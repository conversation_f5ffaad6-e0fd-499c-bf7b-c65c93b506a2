<?php

echo "=== 复制框架文件到boyouphp目录 ===\n\n";

$sourceDir = 'boyouphp';
$targetDir = 'boyouphp';

// 确保目标目录存在
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0755, true);
    echo "✓ 创建目录: $targetDir\n";
}

// 需要复制的文件列表
$filesToCopy = [
    'array.func.php',
    'cache.func.php', 
    'cache_apc.class.php',
    'cache_memcached.class.php',
    'cache_mysql.class.php',
    'cache_redis.class.php',
    'cache_xcache.class.php',
    'cache_yac.class.php',
    'db.func.php',
    'db_mysql.class.php',
    'db_pdo_mysql.class.php',
    'db_pdo_sqlite.class.php',
    'db_pdo_mongodb.class.php',
    'image.func.php',
    'misc.func.php',
    'xn_encrypt.func.php',
    'xn_html_safe.func.php',
    'xn_send_mail.func.php',
    'xn_zip.func.php',
    'modern_error_handler.class.php',
    'performance_monitor.class.php',
    'LICENSE.txt',
    'README.txt'
];

$copiedCount = 0;
$failedFiles = [];

foreach ($filesToCopy as $file) {
    $sourcePath = $sourceDir . '/' . $file;
    $targetPath = $targetDir . '/' . $file;
    
    if (file_exists($sourcePath)) {
        $content = file_get_contents($sourcePath);
        if ($content !== false) {
            if (file_put_contents($targetPath, $content) !== false) {
                echo "✓ 复制: $file\n";
                $copiedCount++;
            } else {
                echo "✗ 写入失败: $file\n";
                $failedFiles[] = $file;
            }
        } else {
            echo "✗ 读取失败: $file\n";
            $failedFiles[] = $file;
        }
    } else {
        echo "! 文件不存在: $file\n";
    }
}

echo "\n=== 复制结果 ===\n";
echo "成功复制: $copiedCount 个文件\n";

if (!empty($failedFiles)) {
    echo "失败文件: " . implode(', ', $failedFiles) . "\n";
} else {
    echo "✓ 所有文件复制成功\n";
}

// 验证关键文件
echo "\n=== 验证文件 ===\n";
$keyFiles = ['boyouphp.php', 'db.func.php', 'misc.func.php'];
foreach ($keyFiles as $file) {
    $path = $targetDir . '/' . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✓ $file (" . number_format($size) . " 字节)\n";
    } else {
        echo "✗ $file 不存在\n";
    }
}

echo "\n=== 完成 ===\n";

?>
