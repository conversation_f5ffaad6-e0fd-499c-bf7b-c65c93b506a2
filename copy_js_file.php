<?php

echo "正在复制 boyou.js 到 boyou.js...\n";

$source = 'view/js/boyou.js';
$destination = 'view/js/boyou.js';

if (file_exists($source)) {
    $content = file_get_contents($source);
    if (file_put_contents($destination, $content)) {
        echo "✓ 成功复制文件\n";
        echo "文件大小: " . strlen($content) . " 字节\n";
    } else {
        echo "✗ 复制失败\n";
    }
} else {
    echo "✗ 源文件不存在\n";
}

// 验证文件是否存在
if (file_exists($destination)) {
    echo "✓ boyou.js 文件已创建\n";
} else {
    echo "✗ boyou.js 文件创建失败\n";
}

?>
