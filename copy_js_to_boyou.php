<?php

echo "复制 boyou.js 到 boyou.js...\n";

$sourceFile = 'view/js/boyou.js';
$targetFile = 'view/js/boyou.js';

if (!file_exists($sourceFile)) {
    echo "错误: 源文件不存在 $sourceFile\n";
    exit(1);
}

$content = file_get_contents($sourceFile);
if ($content === false) {
    echo "错误: 无法读取源文件\n";
    exit(1);
}

// 写入目标文件
if (file_put_contents($targetFile, $content) !== false) {
    echo "成功创建 boyou.js\n";
    echo "文件大小: " . number_format(strlen($content)) . " 字节\n";
} else {
    echo "错误: 无法创建目标文件\n";
    exit(1);
}

// 验证文件
if (file_exists($targetFile)) {
    $newSize = filesize($targetFile);
    echo "验证: boyou.js 文件大小 " . number_format($newSize) . " 字节\n";
    echo "✓ boyou.js 创建成功\n";
} else {
    echo "✗ boyou.js 创建失败\n";
}

?>
