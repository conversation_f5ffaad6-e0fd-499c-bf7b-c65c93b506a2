<?php

echo "创建 boyou.js 文件...\n";

// 读取 xiuno.js 的内容
$content = file_get_contents('view/js/xiuno.js');

if ($content === false) {
    echo "错误：无法读取 xiuno.js 文件\n";
    exit(1);
}

// 写入到 boyou.js
$result = file_put_contents('view/js/boyou.js', $content);

if ($result === false) {
    echo "错误：无法创建 boyou.js 文件\n";
    exit(1);
}

echo "成功创建 boyou.js 文件\n";
echo "文件大小: " . number_format(strlen($content)) . " 字节\n";

// 验证文件是否创建成功
if (file_exists('view/js/boyou.js')) {
    $newSize = filesize('view/js/boyou.js');
    echo "验证：boyou.js 文件大小 " . number_format($newSize) . " 字节\n";
    
    if ($newSize == strlen($content)) {
        echo "✓ 文件复制成功\n";
    } else {
        echo "✗ 文件大小不匹配\n";
    }
} else {
    echo "✗ boyou.js 文件不存在\n";
}

?>
