<?php

// 启用错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 调试500错误 ===\n";

try {
    echo "1. 检查基本PHP功能...\n";
    echo "PHP版本: " . PHP_VERSION . "\n";
    
    echo "2. 检查文件存在性...\n";
    $files = ['index.php', 'conf/conf.php', 'boyouphp/boyouphp.php', 'data/boyou_bbs.db'];
    foreach ($files as $file) {
        echo "  $file: " . (file_exists($file) ? '存在' : '不存在') . "\n";
    }
    
    echo "3. 检查配置文件...\n";
    if (file_exists('conf/conf.php')) {
        $conf = include 'conf/conf.php';
        echo "  配置加载: " . (is_array($conf) ? '成功' : '失败') . "\n";
        if (is_array($conf)) {
            echo "  数据库类型: " . ($conf['db']['type'] ?? '未设置') . "\n";
            echo "  数据库路径: " . ($conf['db']['pdo_sqlite']['master']['path'] ?? '未设置') . "\n";
        }
    }
    
    echo "4. 检查数据库连接...\n";
    if (file_exists('data/boyou_bbs.db')) {
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            echo "  数据库连接: 成功\n";
            
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "  表数量: " . count($tables) . "\n";
            echo "  表列表: " . implode(', ', $tables) . "\n";
        } catch (Exception $e) {
            echo "  数据库连接失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "5. 测试框架加载...\n";
    
    // 设置基本常量
    define('DEBUG', 1);
    define('APP_PATH', __DIR__.'/');
    define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    
    // 加载配置
    if (file_exists('conf/conf.php')) {
        $conf = include APP_PATH.'conf/conf.php';
        $_SERVER['conf'] = $conf;
        echo "  配置设置: 成功\n";
    }
    
    // 尝试加载框架
    if (file_exists('boyouphp/boyouphp.php')) {
        include BOYOUPHP_PATH.'boyouphp.php';
        echo "  框架加载: 成功\n";
    } else {
        echo "  框架文件不存在\n";
    }
    
    echo "6. 测试模型加载...\n";
    if (file_exists('model.inc.php')) {
        include _include(APP_PATH.'model.inc.php');
        echo "  模型加载: 成功\n";
    } else {
        echo "  模型文件不存在\n";
    }
    
    echo "7. 测试数据库初始化...\n";
    if (isset($_SERVER['db'])) {
        echo "  数据库对象: 已创建\n";
    } else {
        echo "  数据库对象: 未创建\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Error $e) {
    echo "致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

?>
