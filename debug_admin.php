<?php

// 调试管理后台登录问题
define('DEBUG', 1);
define('APP_PATH', './');
define('ADMIN_PATH', './admin/');

// 包含必要的文件
include './index.php';

echo "<h1>管理后台登录调试信息</h1>";

echo "<h2>1. 当前用户信息</h2>";
$user = G('user');
if ($user) {
    echo "<pre>";
    print_r($user);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>用户未登录</p>";
}

echo "<h2>2. 用户组信息</h2>";
$gid = G('gid');
echo "<p>当前用户组ID: " . ($gid ? $gid : '未设置') . "</p>";

echo "<h2>3. 管理员检查</h2>";
if (function_exists('user_is_admin')) {
    $is_admin = user_is_admin($gid);
    echo "<p>是否为管理员: " . ($is_admin ? '是' : '否') . "</p>";
} else {
    echo "<p style='color: red;'>user_is_admin 函数不存在</p>";
}

echo "<h2>4. 数据库连接状态</h2>";
$db = G('db');
if ($db) {
    echo "<p style='color: green;'>数据库连接正常</p>";
    echo "<p>数据库类型: " . get_class($db) . "</p>";
} else {
    echo "<p style='color: red;'>数据库连接失败</p>";
}

echo "<h2>5. 配置信息</h2>";
$conf = G('conf');
echo "<p>系统版本: " . $conf['version'] . "</p>";
echo "<p>安装状态: " . ($conf['installed'] ? '已安装' : '未安装') . "</p>";

echo "<h2>6. Session信息</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>7. Cookie信息</h2>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";

echo "<h2>8. 管理员令牌检查</h2>";
$admin_token = param('bbs_admin_token');
echo "<p>管理员令牌: " . ($admin_token ? '存在' : '不存在') . "</p>";

if (function_exists('csrf_token_generate')) {
    echo "<h2>9. CSRF令牌测试</h2>";
    $csrf_token = csrf_token_generate();
    echo "<p>CSRF令牌: " . $csrf_token . "</p>";
} else {
    echo "<p style='color: red;'>csrf_token_generate 函数不存在</p>";
}

echo "<h2>10. 建议操作</h2>";
if (!$user) {
    echo "<p><a href='/?user-login.htm'>请先登录前台用户</a></p>";
} elseif (!user_is_admin($gid)) {
    echo "<p style='color: red;'>当前用户不是管理员</p>";
    echo "<p>需要将用户组设置为管理员组</p>";
} else {
    echo "<p style='color: green;'>用户状态正常，可以访问管理后台</p>";
    echo "<p><a href='/admin/'>访问管理后台</a></p>";
}

?>
