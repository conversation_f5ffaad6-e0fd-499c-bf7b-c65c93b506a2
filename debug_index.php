<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "=== 调试 index.php 执行流程 ===\n";

try {
    echo "1. 开始执行...\n";
    
    // 检查基本环境
    echo "2. PHP版本: " . PHP_VERSION . "\n";
    echo "3. 当前目录: " . getcwd() . "\n";
    
    // 保护敏感路径
    echo "4. 检查路径保护...\n";
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    echo "   请求URI: $request_uri\n";
    
    $protected_paths = ['/conf/', '/data/', '/log/', '/tmp/', '/.git/', '/admin/route/', '/model/'];
    foreach ($protected_paths as $path) {
        if (strpos($request_uri, $path) !== false) {
            echo "   路径被保护，退出\n";
            http_response_code(403);
            exit('Access Denied');
        }
    }
    echo "   路径检查通过\n";
    
    // 保护敏感文件扩展名
    echo "5. 检查文件扩展名保护...\n";
    $protected_extensions = ['.db', '.sql', '.log', '.conf', '.bak', '.backup'];
    foreach ($protected_extensions as $ext) {
        if (substr($request_uri, -strlen($ext)) === $ext) {
            echo "   文件扩展名被保护，退出\n";
            http_response_code(403);
            exit('Access Denied');
        }
    }
    echo "   文件扩展名检查通过\n";
    
    // 设置常量
    echo "6. 设置常量...\n";
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', __DIR__.'/');
    if (!defined('ADMIN_PATH')) define('ADMIN_PATH', APP_PATH.'admin/');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    echo "   常量设置完成\n";
    
    // 加载配置
    echo "7. 加载配置...\n";
    if (file_exists(APP_PATH.'conf/conf.php')) {
        $conf = include APP_PATH.'conf/conf.php';
        $_SERVER['conf'] = $conf;
        echo "   配置加载成功\n";
        echo "   数据库类型: " . ($conf['db']['type'] ?? '未设置') . "\n";
    } else {
        echo "   配置文件不存在\n";
        exit;
    }
    
    // 加载安全配置
    echo "8. 加载安全配置...\n";
    if (file_exists(APP_PATH.'conf/security.php')) {
        $security_conf = include APP_PATH.'conf/security.php';
        echo "   安全配置加载成功\n";
    } else {
        echo "   安全配置文件不存在\n";
    }
    
    // 加载框架
    echo "9. 加载框架...\n";
    if (file_exists(BOYOUPHP_PATH.'boyouphp.php')) {
        include BOYOUPHP_PATH.'boyouphp.php';
        echo "   框架加载成功\n";
    } else {
        echo "   框架文件不存在\n";
        exit;
    }
    
    // 检查数据库连接
    echo "10. 检查数据库连接...\n";
    if (isset($_SERVER['db'])) {
        echo "    数据库对象已创建\n";
    } else {
        echo "    数据库对象未创建\n";
    }
    
    // 加载模型
    echo "11. 加载模型...\n";
    if (file_exists(APP_PATH.'model.inc.php')) {
        include _include(APP_PATH.'model.inc.php');
        echo "    模型加载成功\n";
    } else {
        echo "    模型文件不存在\n";
        exit;
    }
    
    // 加载路由分发器
    echo "12. 加载路由分发器...\n";
    if (file_exists(APP_PATH.'index.inc.php')) {
        echo "    开始包含 index.inc.php...\n";
        include APP_PATH.'index.inc.php';
        echo "    路由分发器加载完成\n";
    } else {
        echo "    路由分发器文件不存在\n";
        exit;
    }
    
    echo "13. 执行完成\n";
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 调试完成 ===\n";

?>
