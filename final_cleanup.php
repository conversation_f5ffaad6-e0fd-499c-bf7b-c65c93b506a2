<?php

echo "=== 最终清理和文件重命名 ===\n\n";

// 1. 创建boyou.js文件
echo "1. 创建 boyou.js 文件...\n";
$sourceJs = 'view/js/boyou.js';
$targetJs = 'view/js/boyou.js';

if (file_exists($sourceJs) && !file_exists($targetJs)) {
    $content = file_get_contents($sourceJs);
    if (file_put_contents($targetJs, $content)) {
        echo "✓ 成功创建 boyou.js\n";
    } else {
        echo "✗ 创建 boyou.js 失败\n";
    }
} elseif (file_exists($targetJs)) {
    echo "✓ boyou.js 已存在\n";
} else {
    echo "✗ 源文件 boyou.js 不存在\n";
}

// 2. 检查并移除xiunophp目录（如果boyouphp已完整）
echo "\n2. 检查目录状态...\n";

if (is_dir('boyouphp')) {
    $boyouFiles = scandir('boyouphp');
    $boyouPhpFiles = array_filter($boyouFiles, function($f) { 
        return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
    });
    echo "✓ boyouphp 目录存在，包含 " . count($boyouPhpFiles) . " 个PHP文件\n";
    
    // 检查关键文件
    $keyFiles = ['boyouphp.php', 'boyouphp.min.php', 'db.func.php', 'misc.func.php'];
    $missingFiles = [];
    foreach ($keyFiles as $file) {
        if (!file_exists("boyouphp/$file")) {
            $missingFiles[] = $file;
        }
    }
    
    if (empty($missingFiles)) {
        echo "✓ 所有关键文件都存在\n";
    } else {
        echo "⚠️  缺少关键文件: " . implode(', ', $missingFiles) . "\n";
    }
} else {
    echo "✗ boyouphp 目录不存在\n";
}

if (is_dir('boyouphp')) {
    echo "⚠️  xiunophp 目录仍然存在\n";
    $xiunoFiles = scandir('boyouphp');
    $xiunoPhpFiles = array_filter($xiunoFiles, function($f) { 
        return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
    });
    echo "   包含 " . count($xiunoPhpFiles) . " 个PHP文件\n";
} else {
    echo "✓ xiunophp 目录已不存在\n";
}

// 3. 检查关键文件中的引用
echo "\n3. 检查关键文件中的引用...\n";

$filesToCheck = [
    'index.php' => ['boyouphp/', 'BOYOUPHP_PATH'],
    'install/index.php' => ['boyouphp/'],
    'view/htm/footer.inc.htm' => ['boyou.js'],
    'admin/view/htm/footer.inc.htm' => ['boyou.js'],
    'install/view/htm/footer.inc.htm' => ['boyou.js'],
];

foreach ($filesToCheck as $file => $expectedRefs) {
    if (!file_exists($file)) {
        echo "⚠️  文件不存在: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $allFound = true;
    
    foreach ($expectedRefs as $ref) {
        if (strpos($content, $ref) !== false) {
            echo "✓ $file 包含 $ref\n";
        } else {
            echo "✗ $file 缺少 $ref\n";
            $allFound = false;
        }
    }
    
    // 检查是否还有xiuno引用
    if (preg_match('/xiuno/i', $content)) {
        echo "⚠️  $file 仍包含 xiuno 引用\n";
    }
}

// 4. 检查版本信息
echo "\n4. 检查版本信息...\n";

if (file_exists('conf/conf.php')) {
    $conf = include 'conf/conf.php';
    
    if (isset($conf['version']) && $conf['version'] === '6.1.0') {
        echo "✓ 配置文件版本正确: {$conf['version']}\n";
    } else {
        echo "⚠️  配置文件版本: " . ($conf['version'] ?? '未设置') . "\n";
    }
    
    if (isset($conf['sitename']) && $conf['sitename'] === 'Boyou BBS') {
        echo "✓ 站点名称正确: {$conf['sitename']}\n";
    } else {
        echo "⚠️  站点名称: " . ($conf['sitename'] ?? '未设置') . "\n";
    }
}

// 5. 生成清理建议
echo "\n5. 清理建议...\n";

$suggestions = [];

// 检查是否可以删除boyou.js
if (file_exists('view/js/boyou.js') && file_exists('view/js/boyou.js')) {
    $xiunoContent = file_get_contents('view/js/boyou.js');
    $boyouContent = file_get_contents('view/js/boyou.js');
    
    if (md5($xiunoContent) === md5($boyouContent)) {
        $suggestions[] = "可以删除 view/js/boyou.js（内容与 boyou.js 相同）";
    } else {
        $suggestions[] = "检查 view/js/boyou.js 和 boyou.js 的差异";
    }
}

// 检查是否可以删除xiunophp目录
if (is_dir('boyouphp') && is_dir('boyouphp')) {
    $suggestions[] = "检查 xiunophp 和 boyouphp 目录的差异，确认后可删除 xiunophp";
}

if (!empty($suggestions)) {
    echo "建议的清理操作:\n";
    foreach ($suggestions as $suggestion) {
        echo "  - $suggestion\n";
    }
} else {
    echo "✓ 没有需要清理的项目\n";
}

// 6. 最终状态报告
echo "\n=== 最终状态报告 ===\n";

$status = [
    'boyouphp_exists' => is_dir('boyouphp'),
    'xiunophp_exists' => is_dir('boyouphp'),
    'boyou_js_exists' => file_exists('view/js/boyou.js'),
    'xiuno_js_exists' => file_exists('view/js/boyou.js'),
    'config_version' => $conf['version'] ?? 'unknown',
    'site_name' => $conf['sitename'] ?? 'unknown'
];

foreach ($status as $key => $value) {
    $display = is_bool($value) ? ($value ? '是' : '否') : $value;
    echo "$key: $display\n";
}

echo "\n=== 清理完成 ===\n";

?>
