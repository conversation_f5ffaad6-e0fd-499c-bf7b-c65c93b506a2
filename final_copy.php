<?php

echo "正在创建 boyou.js...\n";

// 读取源文件
$sourceFile = 'view/js/boyou.js';
if (!file_exists($sourceFile)) {
    echo "错误：源文件 $sourceFile 不存在\n";
    exit(1);
}

$content = file_get_contents($sourceFile);
if ($content === false) {
    echo "错误：无法读取源文件\n";
    exit(1);
}

// 写入目标文件
$targetFile = 'view/js/boyou.js';
$result = file_put_contents($targetFile, $content);

if ($result === false) {
    echo "错误：无法创建目标文件\n";
    exit(1);
}

echo "成功创建 boyou.js\n";
echo "文件大小: " . number_format(strlen($content)) . " 字节\n";

// 验证
if (file_exists($targetFile)) {
    $targetSize = filesize($targetFile);
    echo "验证：目标文件大小 " . number_format($targetSize) . " 字节\n";
    
    if ($targetSize == strlen($content)) {
        echo "✓ 文件复制成功\n";
    } else {
        echo "✗ 文件大小不匹配\n";
    }
} else {
    echo "✗ 目标文件不存在\n";
}

// 检查文件内容
$targetContent = file_get_contents($targetFile);
if (strpos($targetContent, 'boyou.js loaded') !== false) {
    echo "✓ 文件内容验证成功\n";
} else {
    echo "✗ 文件内容验证失败\n";
}

echo "完成\n";

?>
