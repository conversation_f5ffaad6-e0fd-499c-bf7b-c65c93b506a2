<?php

echo "=== 最终验证：xiunophp 到 boyouphp 转移结果 ===\n\n";

$results = [];

// 1. 检查目录状态
echo "1. 检查目录状态...\n";
$results['xiunophp_exists'] = is_dir('boyouphp');
$results['boyouphp_exists'] = is_dir('boyouphp');

if ($results['boyouphp_exists']) {
    echo "✓ boyouphp 目录存在\n";
    $boyouFiles = scandir('boyouphp');
    $phpFiles = array_filter($boyouFiles, function($f) { 
        return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
    });
    echo "  包含 " . count($phpFiles) . " 个PHP文件\n";
    $results['boyouphp_file_count'] = count($phpFiles);
} else {
    echo "✗ boyouphp 目录不存在\n";
    $results['boyouphp_file_count'] = 0;
}

if ($results['xiunophp_exists']) {
    echo "⚠️  xiunophp 目录仍然存在\n";
    $xiunoFiles = scandir('boyouphp');
    $phpFiles = array_filter($xiunoFiles, function($f) { 
        return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
    });
    echo "  包含 " . count($phpFiles) . " 个PHP文件\n";
    $results['xiunophp_file_count'] = count($phpFiles);
} else {
    echo "✓ xiunophp 目录已不存在\n";
    $results['xiunophp_file_count'] = 0;
}

// 2. 检查关键文件
echo "\n2. 检查关键文件...\n";
$keyFiles = [
    'boyouphp/boyouphp.php',
    'boyouphp/boyouphp.min.php', 
    'boyouphp/db.func.php',
    'boyouphp/cache.func.php',
    'boyouphp/misc.func.php',
    'boyouphp/array.func.php',
    'boyouphp/image.func.php',
    'boyouphp/xn_encrypt.func.php',
    'boyouphp/db_mysql.class.php',
    'boyouphp/db_pdo_mysql.class.php',
    'boyouphp/cache_redis.class.php',
    'boyouphp/cache_apc.class.php'
];

$missingFiles = [];
foreach ($keyFiles as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✓ $file (" . number_format($size) . " 字节)\n";
    } else {
        echo "✗ $file 缺失\n";
        $missingFiles[] = $file;
    }
}
$results['missing_files'] = $missingFiles;

// 3. 检查JavaScript文件
echo "\n3. 检查JavaScript文件...\n";
$results['xiuno_js_exists'] = file_exists('view/js/boyou.js');
$results['boyou_js_exists'] = file_exists('view/js/boyou.js');

if ($results['boyou_js_exists']) {
    echo "✓ boyou.js 存在\n";
    $size = filesize('view/js/boyou.js');
    echo "  文件大小: " . number_format($size) . " 字节\n";
} else {
    echo "✗ boyou.js 不存在\n";
}

if ($results['xiuno_js_exists']) {
    echo "⚠️  boyou.js 仍然存在\n";
} else {
    echo "✓ boyou.js 已不存在\n";
}

// 4. 检查文件引用
echo "\n4. 检查文件引用...\n";
$referenceFiles = [
    'index.php' => ['boyouphp/', 'BOYOUPHP_PATH'],
    'install/index.php' => ['boyouphp/'],
    'view/htm/footer.inc.htm' => ['boyou.js'],
    'admin/view/htm/footer.inc.htm' => ['boyou.js'],
    'install/view/htm/footer.inc.htm' => ['boyou.js'],
];

$referenceIssues = [];
foreach ($referenceFiles as $file => $expectedRefs) {
    if (!file_exists($file)) {
        $referenceIssues[] = "文件不存在: $file";
        continue;
    }
    
    $content = file_get_contents($file);
    foreach ($expectedRefs as $ref) {
        if (strpos($content, $ref) === false) {
            $referenceIssues[] = "$file 缺少引用: $ref";
        } else {
            echo "✓ $file 包含 $ref\n";
        }
    }
    
    // 检查是否还有xiuno引用
    if (preg_match('/xiuno(?!\.com)/i', $content)) {
        $referenceIssues[] = "$file 仍包含 xiuno 引用";
    }
}
$results['reference_issues'] = $referenceIssues;

// 5. 检查品牌信息
echo "\n5. 检查品牌信息...\n";
$brandFiles = [
    'view/htm/footer_nav.inc.htm',
    'admin/view/htm/footer.inc.htm',
    'tmp/view_htm_footer_nav.inc.htm',
    'tmp/admin_view_htm_footer.inc.htm'
];

$brandIssues = [];
foreach ($brandFiles as $file) {
    if (!file_exists($file)) {
        continue;
    }
    
    $content = file_get_contents($file);
    if (strpos($content, 'Boyou BBS') !== false) {
        echo "✓ $file 包含 Boyou BBS 品牌\n";
    } else {
        $brandIssues[] = "$file 缺少 Boyou BBS 品牌";
    }
    
    if (strpos($content, 'Boyou BBS') !== false) {
        $brandIssues[] = "$file 仍包含 Boyou BBS 品牌";
    }
}
$results['brand_issues'] = $brandIssues;

// 6. 检查版本信息
echo "\n6. 检查版本信息...\n";
if (file_exists('conf/conf.php')) {
    $conf = include 'conf/conf.php';
    $results['config_version'] = $conf['version'] ?? 'unknown';
    $results['site_name'] = $conf['sitename'] ?? 'unknown';
    
    if ($results['config_version'] === '6.1.0') {
        echo "✓ 配置文件版本正确: {$results['config_version']}\n";
    } else {
        echo "⚠️  配置文件版本: {$results['config_version']}\n";
    }
    
    if ($results['site_name'] === 'Boyou BBS') {
        echo "✓ 站点名称正确: {$results['site_name']}\n";
    } else {
        echo "⚠️  站点名称: {$results['site_name']}\n";
    }
}

// 7. 生成总结报告
echo "\n=== 转移结果总结 ===\n";

$score = 0;
$maxScore = 10;

// 评分标准
if ($results['boyouphp_exists']) $score++;
if (!$results['xiunophp_exists']) $score++;
if (empty($results['missing_files'])) $score++;
if ($results['boyou_js_exists']) $score++;
if (!$results['xiuno_js_exists']) $score++;
if (empty($results['reference_issues'])) $score++;
if (empty($results['brand_issues'])) $score++;
if ($results['config_version'] === '6.1.0') $score++;
if ($results['site_name'] === 'Boyou BBS') $score++;
if ($results['boyouphp_file_count'] >= 10) $score++;

echo "转移完成度: $score/$maxScore (" . round($score/$maxScore*100) . "%)\n\n";

if ($score >= 8) {
    echo "🎉 转移基本完成！\n";
} elseif ($score >= 6) {
    echo "⚠️  转移大部分完成，还有一些问题需要处理\n";
} else {
    echo "❌ 转移未完成，需要进一步处理\n";
}

// 列出剩余问题
$allIssues = array_merge($results['reference_issues'], $results['brand_issues']);
if (!empty($allIssues)) {
    echo "\n剩余问题:\n";
    foreach ($allIssues as $issue) {
        echo "  - $issue\n";
    }
}

if (!empty($results['missing_files'])) {
    echo "\n缺失文件:\n";
    foreach ($results['missing_files'] as $file) {
        echo "  - $file\n";
    }
}

// 清理建议
echo "\n=== 清理建议 ===\n";
$suggestions = [];

if ($results['xiunophp_exists'] && $results['boyouphp_exists'] && $results['boyouphp_file_count'] >= 10) {
    $suggestions[] = "可以考虑删除 xiunophp 目录（请先备份）";
}

if ($results['xiuno_js_exists'] && $results['boyou_js_exists']) {
    $suggestions[] = "可以考虑删除 view/js/boyou.js";
}

if (file_exists('tmp/xiunophp_xn_send_mail.func.php')) {
    $suggestions[] = "重命名 tmp/xiunophp_xn_send_mail.func.php 为 tmp/boyouphp_xn_send_mail.func.php";
}

if (!empty($suggestions)) {
    foreach ($suggestions as $suggestion) {
        echo "  - $suggestion\n";
    }
} else {
    echo "✓ 没有需要清理的项目\n";
}

echo "\n=== 验证完成 ===\n";

// 保存结果到文件
file_put_contents('transfer_verification_report.json', json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n详细报告已保存到: transfer_verification_report.json\n";

?>
