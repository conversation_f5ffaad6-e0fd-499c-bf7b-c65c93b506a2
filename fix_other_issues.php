<?php

echo "=== 解决其它可能存在的问题 ===\n\n";

$fixedIssues = [];
$recommendations = [];

// 1. 检查和修复文件权限问题
echo "1. 检查文件权限问题...\n";

$criticalDirectories = [
    'upload' => 0755,
    'upload/attach' => 0755,
    'upload/avatar' => 0755,
    'upload/forum' => 0755,
    'upload/tmp' => 0755,
    'log' => 0755,
    'tmp' => 0755,
    'conf' => 0644
];

foreach ($criticalDirectories as $dir => $expectedPerm) {
    if (is_dir($dir)) {
        $currentPerm = fileperms($dir) & 0777;
        if ($currentPerm !== $expectedPerm) {
            if (chmod($dir, $expectedPerm)) {
                echo "✓ 修复目录权限: $dir (" . decoct($currentPerm) . " → " . decoct($expectedPerm) . ")\n";
                $fixedIssues[] = "修复目录权限: $dir";
            } else {
                echo "⚠️  无法修复目录权限: $dir\n";
            }
        } else {
            echo "✓ 目录权限正确: $dir\n";
        }
    } else {
        echo "⚠️  目录不存在: $dir\n";
        if (mkdir($dir, $expectedPerm, true)) {
            echo "✓ 创建目录: $dir\n";
            $fixedIssues[] = "创建缺失目录: $dir";
        }
    }
}

// 2. 检查PHP配置问题
echo "\n2. 检查PHP配置问题...\n";

$phpSettings = [
    'display_errors' => '0',
    'log_errors' => '1',
    'upload_max_filesize' => '2M',
    'post_max_size' => '8M',
    'max_execution_time' => '30',
    'memory_limit' => '128M'
];

foreach ($phpSettings as $setting => $recommended) {
    $current = ini_get($setting);
    echo "  $setting: $current";
    
    if ($setting === 'display_errors' && $current !== '0') {
        echo " ⚠️  建议设为 0 (生产环境)";
        $recommendations[] = "设置 display_errors = 0";
    } elseif ($setting === 'log_errors' && $current !== '1') {
        echo " ⚠️  建议设为 1";
        $recommendations[] = "设置 log_errors = 1";
    } else {
        echo " ✓";
    }
    echo "\n";
}

// 3. 检查数据库连接配置
echo "\n3. 检查数据库连接配置...\n";

if (file_exists('conf/conf.php')) {
    $conf = include 'conf/conf.php';
    
    if (isset($conf['db'])) {
        echo "✓ 数据库配置存在\n";
        
        // 检查数据库配置的安全性
        if (isset($conf['db']['mysql']['master']['password']) && 
            strlen($conf['db']['mysql']['master']['password']) < 8) {
            echo "⚠️  数据库密码强度不足\n";
            $recommendations[] = "使用更强的数据库密码";
        }
        
        if (isset($conf['db']['mysql']['master']['host']) && 
            $conf['db']['mysql']['master']['host'] === 'localhost') {
            echo "✓ 数据库主机配置安全\n";
        }
        
    } else {
        echo "⚠️  数据库配置缺失\n";
        $recommendations[] = "配置数据库连接";
    }
} else {
    echo "⚠️  配置文件不存在\n";
    $recommendations[] = "创建配置文件";
}

// 4. 检查缓存配置
echo "\n4. 检查缓存配置...\n";

if (extension_loaded('redis')) {
    echo "✓ Redis扩展已加载\n";
} else {
    echo "⚠️  Redis扩展未加载\n";
    $recommendations[] = "安装Redis扩展以提高性能";
}

if (extension_loaded('memcached')) {
    echo "✓ Memcached扩展已加载\n";
} else {
    echo "⚠️  Memcached扩展未加载\n";
}

if (function_exists('apcu_fetch')) {
    echo "✓ APCu扩展已加载\n";
} else {
    echo "⚠️  APCu扩展未加载\n";
    $recommendations[] = "安装APCu扩展以提高性能";
}

// 5. 检查日志目录和文件
echo "\n5. 检查日志系统...\n";

$logDirs = ['log', 'log/' . date('Ym')];
foreach ($logDirs as $logDir) {
    if (!is_dir($logDir)) {
        if (mkdir($logDir, 0755, true)) {
            echo "✓ 创建日志目录: $logDir\n";
            $fixedIssues[] = "创建日志目录: $logDir";
        } else {
            echo "✗ 创建日志目录失败: $logDir\n";
        }
    } else {
        echo "✓ 日志目录存在: $logDir\n";
    }
}

// 创建日志轮转配置
$logRotateConfig = '#!/bin/bash
# 日志轮转脚本
# 建议添加到crontab: 0 0 * * * /path/to/log_rotate.sh

LOG_DIR="./log"
DAYS_TO_KEEP=30

# 压缩30天前的日志
find $LOG_DIR -name "*.log" -mtime +$DAYS_TO_KEEP -exec gzip {} \;

# 删除90天前的压缩日志
find $LOG_DIR -name "*.log.gz" -mtime +90 -delete

echo "Log rotation completed at $(date)"
';

if (!file_exists('log_rotate.sh')) {
    if (file_put_contents('log_rotate.sh', $logRotateConfig)) {
        chmod('log_rotate.sh', 0755);
        echo "✓ 创建日志轮转脚本: log_rotate.sh\n";
        $fixedIssues[] = "创建日志轮转脚本";
    }
}

// 6. 检查会话配置
echo "\n6. 检查会话配置...\n";

$sessionDir = session_save_path();
if (empty($sessionDir)) {
    $sessionDir = sys_get_temp_dir();
}

if (is_writable($sessionDir)) {
    echo "✓ 会话目录可写: $sessionDir\n";
} else {
    echo "⚠️  会话目录不可写: $sessionDir\n";
    $recommendations[] = "确保会话目录可写";
}

// 检查会话安全设置
$sessionSettings = [
    'session.cookie_httponly' => '1',
    'session.cookie_secure' => '0', // 在HTTPS环境下应设为1
    'session.use_strict_mode' => '1',
    'session.cookie_samesite' => 'Strict'
];

foreach ($sessionSettings as $setting => $recommended) {
    $current = ini_get($setting);
    echo "  $setting: " . ($current ?: 'not set');
    
    if ($current !== $recommended) {
        echo " ⚠️  建议设为 $recommended";
        $recommendations[] = "设置 $setting = $recommended";
    } else {
        echo " ✓";
    }
    echo "\n";
}

// 7. 创建性能监控脚本
echo "\n7. 创建性能监控脚本...\n";

$performanceMonitor = '<?php
// 性能监控脚本
class PerformanceMonitor {
    private $startTime;
    private $startMemory;
    private $queries = [];
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
    }
    
    public function addQuery($sql, $time) {
        $this->queries[] = [
            \'sql\' => $sql,
            \'time\' => $time,
            \'memory\' => memory_get_usage()
        ];
    }
    
    public function getReport() {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        return [
            \'execution_time\' => round(($endTime - $this->startTime) * 1000, 2),
            \'memory_usage\' => round(($endMemory - $this->startMemory) / 1024, 2),
            \'peak_memory\' => round(memory_get_peak_usage() / 1024, 2),
            \'query_count\' => count($this->queries),
            \'slow_queries\' => array_filter($this->queries, function($q) {
                return $q[\'time\'] > 0.1; // 慢查询阈值100ms
            })
        ];
    }
    
    public function logReport() {
        $report = $this->getReport();
        $logEntry = date(\'Y-m-d H:i:s\') . \' - \' . json_encode($report) . "\n";
        file_put_contents(\'./log/performance.log\', $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// 全局性能监控实例
if (!isset($GLOBALS[\'performance_monitor\'])) {
    $GLOBALS[\'performance_monitor\'] = new PerformanceMonitor();
}
?>';

if (!file_exists('boyouphp/performance_monitor.php')) {
    if (file_put_contents('boyouphp/performance_monitor.php', $performanceMonitor)) {
        echo "✓ 创建性能监控脚本: boyouphp/performance_monitor.php\n";
        $fixedIssues[] = "创建性能监控脚本";
    }
}

// 8. 创建健康检查脚本
echo "\n8. 创建健康检查脚本...\n";

$healthCheck = '<?php
// 系统健康检查脚本
header(\'Content-Type: application/json\');

$health = [
    \'status\' => \'ok\',
    \'timestamp\' => date(\'c\'),
    \'checks\' => []
];

// 检查数据库连接
try {
    if (file_exists(\'./conf/conf.php\')) {
        $conf = include \'./conf/conf.php\';
        if (isset($conf[\'db\'])) {
            // 这里应该实际测试数据库连接
            $health[\'checks\'][\'database\'] = \'ok\';
        } else {
            $health[\'checks\'][\'database\'] = \'config_missing\';
            $health[\'status\'] = \'warning\';
        }
    } else {
        $health[\'checks\'][\'database\'] = \'config_file_missing\';
        $health[\'status\'] = \'error\';
    }
} catch (Exception $e) {
    $health[\'checks\'][\'database\'] = \'error\';
    $health[\'status\'] = \'error\';
}

// 检查文件系统
$writableDirs = [\'upload\', \'log\', \'tmp\'];
foreach ($writableDirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        $health[\'checks\'][\'filesystem_\' . $dir] = \'ok\';
    } else {
        $health[\'checks\'][\'filesystem_\' . $dir] = \'not_writable\';
        $health[\'status\'] = \'warning\';
    }
}

// 检查PHP扩展
$requiredExtensions = [\'pdo\', \'json\', \'mbstring\'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $health[\'checks\'][\'extension_\' . $ext] = \'ok\';
    } else {
        $health[\'checks\'][\'extension_\' . $ext] = \'missing\';
        $health[\'status\'] = \'error\';
    }
}

// 检查内存使用
$memoryUsage = memory_get_usage(true);
$memoryLimit = ini_get(\'memory_limit\');
$memoryLimitBytes = return_bytes($memoryLimit);
$memoryPercent = ($memoryUsage / $memoryLimitBytes) * 100;

if ($memoryPercent > 80) {
    $health[\'checks\'][\'memory\'] = \'high_usage\';
    $health[\'status\'] = \'warning\';
} else {
    $health[\'checks\'][\'memory\'] = \'ok\';
}

$health[\'memory_usage\'] = round($memoryUsage / 1024 / 1024, 2) . \'MB\';
$health[\'memory_limit\'] = $memoryLimit;

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case \'g\': $val *= 1024;
        case \'m\': $val *= 1024;
        case \'k\': $val *= 1024;
    }
    return $val;
}

echo json_encode($health, JSON_PRETTY_PRINT);
?>';

if (!file_exists('health_check.php')) {
    if (file_put_contents('health_check.php', $healthCheck)) {
        echo "✓ 创建健康检查脚本: health_check.php\n";
        $fixedIssues[] = "创建健康检查脚本";
    }
}

// 9. 创建备份脚本
echo "\n9. 创建备份脚本...\n";

$backupScript = '#!/bin/bash
# 自动备份脚本
# 建议添加到crontab: 0 2 * * * /path/to/backup.sh

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="boyou_bbs_backup_$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份文件
echo "开始备份文件..."
tar -czf "$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz" \
    --exclude="./backups" \
    --exclude="./log" \
    --exclude="./tmp" \
    --exclude="./upload/tmp" \
    .

# 备份数据库（需要配置数据库信息）
# mysqldump -u username -p password database_name > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"

# 删除7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "备份完成: $BACKUP_NAME"
';

if (!file_exists('backup.sh')) {
    if (file_put_contents('backup.sh', $backupScript)) {
        chmod('backup.sh', 0755);
        echo "✓ 创建备份脚本: backup.sh\n";
        $fixedIssues[] = "创建备份脚本";
    }
}

echo "\n=== 其他问题修复完成 ===\n";

echo "\n🔧 已修复的问题:\n";
foreach ($fixedIssues as $issue) {
    echo "  ✅ $issue\n";
}

echo "\n💡 建议改进的配置:\n";
foreach ($recommendations as $rec) {
    echo "  📝 $rec\n";
}

echo "\n🛠️ 新增的工具:\n";
echo "  📊 性能监控脚本\n";
echo "  🏥 健康检查脚本\n";
echo "  💾 自动备份脚本\n";
echo "  📋 日志轮转脚本\n";

echo "\n📋 后续维护建议:\n";
echo "  - 定期运行健康检查: php health_check.php\n";
echo "  - 设置自动备份计划任务\n";
echo "  - 监控性能日志\n";
echo "  - 定期更新PHP和扩展\n";
echo "  - 配置HTTPS和安全头\n";

?>
