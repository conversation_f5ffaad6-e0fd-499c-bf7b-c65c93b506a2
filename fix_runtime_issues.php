<?php

echo "=== 修复运行时问题 ===\n\n";

$fixedIssues = [];
$errors = [];

// 1. 修复函数重复声明问题
echo "1. 修复函数重复声明问题...\n";

// 检查misc.func.php中是否有重复的CSRF函数
$miscFuncPath = 'boyouphp/misc.func.php';
if (file_exists($miscFuncPath)) {
    $content = file_get_contents($miscFuncPath);
    
    // 检查是否有重复的CSRF函数
    if (strpos($content, 'function csrf_token_generate') !== false) {
        echo "发现重复的CSRF函数，正在移除...\n";
        
        // 移除重复的CSRF函数
        $patterns = [
            '/function csrf_token_generate\(\)[^}]*\{[^}]*\}/s',
            '/function csrf_token_verify\([^}]*\{[^}]*\}/s',
            '/function csrf_token_field\(\)[^}]*\{[^}]*\}/s',
            '/function csrf_token_meta\(\)[^}]*\{[^}]*\}/s'
        ];
        
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        // 清理多余的空行
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
        
        if (file_put_contents($miscFuncPath, $content)) {
            echo "✓ 已移除misc.func.php中重复的CSRF函数\n";
            $fixedIssues[] = "移除重复的CSRF函数";
        } else {
            echo "✗ 修复misc.func.php失败\n";
            $errors[] = "修复misc.func.php失败";
        }
    } else {
        echo "✓ misc.func.php中没有重复的CSRF函数\n";
    }
}

// 2. 修复配置文件权限问题
echo "\n2. 修复配置文件权限问题...\n";

$configFiles = [
    'conf/conf.default.php',
    'conf/conf.php'
];

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        $currentPerm = fileperms($file) & 0777;
        if ($currentPerm !== 0644) {
            if (chmod($file, 0644)) {
                echo "✓ 修复文件权限: $file\n";
                $fixedIssues[] = "修复文件权限: $file";
            } else {
                echo "✗ 修复文件权限失败: $file\n";
                $errors[] = "修复文件权限失败: $file";
            }
        } else {
            echo "✓ 文件权限正确: $file\n";
        }
    } else {
        echo "⚠️  文件不存在: $file\n";
    }
}

// 3. 修复语言文件路径问题
echo "\n3. 修复语言文件路径问题...\n";

// 检查install/index.php中的语言文件加载
$installIndexPath = 'install/index.php';
if (file_exists($installIndexPath)) {
    $content = file_get_contents($installIndexPath);
    
    // 修复语言文件路径
    $patterns = [
        '/include.*?conf\.default\.php/' => "include '../conf/conf.default.php'",
        '/\$lang\s*=\s*include.*?lang\/\/bbs\.php/' => '$lang = include "../lang/zh-cn/bbs.php"',
        '/\$lang_install\s*=\s*include.*?lang\/\/bbs_install\.php/' => '$lang_install = include "../lang/zh-cn/bbs_install.php"'
    ];
    
    $changed = false;
    foreach ($patterns as $pattern => $replacement) {
        $newContent = preg_replace('/' . str_replace('/', '\/', $pattern) . '/', $replacement, $content);
        if ($newContent !== $content) {
            $content = $newContent;
            $changed = true;
        }
    }
    
    if ($changed) {
        if (file_put_contents($installIndexPath, $content)) {
            echo "✓ 修复install/index.php中的路径问题\n";
            $fixedIssues[] = "修复install/index.php路径问题";
        } else {
            echo "✗ 修复install/index.php失败\n";
            $errors[] = "修复install/index.php失败";
        }
    } else {
        echo "✓ install/index.php路径正确\n";
    }
}

// 4. 创建缺失的配置文件
echo "\n4. 创建缺失的配置文件...\n";

if (!file_exists('conf/conf.php')) {
    // 复制默认配置文件
    if (file_exists('conf/conf.default.php')) {
        if (copy('conf/conf.default.php', 'conf/conf.php')) {
            echo "✓ 创建conf/conf.php配置文件\n";
            $fixedIssues[] = "创建conf/conf.php配置文件";
        } else {
            echo "✗ 创建conf/conf.php失败\n";
            $errors[] = "创建conf/conf.php失败";
        }
    } else {
        echo "⚠️  conf.default.php不存在，无法创建conf.php\n";
    }
} else {
    echo "✓ conf/conf.php已存在\n";
}

// 5. 修复tmp_path配置
echo "\n5. 修复tmp_path配置...\n";

if (file_exists('conf/conf.php')) {
    $content = file_get_contents('conf/conf.php');
    
    // 检查是否有tmp_path配置
    if (strpos($content, 'tmp_path') === false) {
        // 添加tmp_path配置
        $tmpPathConfig = "\n// 临时文件路径\n\$conf['tmp_path'] = './tmp/';\n";
        
        // 在文件末尾添加配置
        $content = str_replace('?>', $tmpPathConfig . '?>', $content);
        
        if (file_put_contents('conf/conf.php', $content)) {
            echo "✓ 添加tmp_path配置到conf/conf.php\n";
            $fixedIssues[] = "添加tmp_path配置";
        } else {
            echo "✗ 添加tmp_path配置失败\n";
            $errors[] = "添加tmp_path配置失败";
        }
    } else {
        echo "✓ tmp_path配置已存在\n";
    }
}

// 6. 检查和修复语言文件
echo "\n6. 检查和修复语言文件...\n";

$langDirs = ['lang/zh-cn', 'lang/en-us'];
$langFiles = ['bbs.php', 'bbs_install.php'];

foreach ($langDirs as $langDir) {
    if (!is_dir($langDir)) {
        echo "⚠️  语言目录不存在: $langDir\n";
        continue;
    }
    
    foreach ($langFiles as $langFile) {
        $filePath = "$langDir/$langFile";
        if (file_exists($filePath)) {
            echo "✓ 语言文件存在: $filePath\n";
        } else {
            echo "⚠️  语言文件缺失: $filePath\n";
        }
    }
}

// 7. 创建简单的首页重定向
echo "\n7. 创建首页处理...\n";

// 检查index.php是否正确处理首页访问
$indexPath = 'index.php';
if (file_exists($indexPath)) {
    $content = file_get_contents($indexPath);
    
    // 检查是否有安装检查
    if (strpos($content, 'install') === false) {
        // 添加安装检查
        $installCheck = '
// 检查是否已安装
if (!file_exists("./conf/conf.php") || !file_exists("./data/install.lock")) {
    header("Location: ./install/");
    exit;
}
';
        
        // 在文件开头添加安装检查
        $content = str_replace('<?php', '<?php' . $installCheck, $content);
        
        if (file_put_contents($indexPath, $content)) {
            echo "✓ 添加安装检查到index.php\n";
            $fixedIssues[] = "添加安装检查";
        } else {
            echo "✗ 修改index.php失败\n";
            $errors[] = "修改index.php失败";
        }
    } else {
        echo "✓ index.php已包含安装检查\n";
    }
}

// 8. 创建安装锁文件目录
echo "\n8. 创建必要的目录...\n";

$requiredDirs = ['data', 'upload/attach', 'upload/avatar', 'upload/forum', 'upload/tmp'];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✓ 创建目录: $dir\n";
            $fixedIssues[] = "创建目录: $dir";
        } else {
            echo "✗ 创建目录失败: $dir\n";
            $errors[] = "创建目录失败: $dir";
        }
    } else {
        echo "✓ 目录已存在: $dir\n";
    }
}

echo "\n=== 运行时问题修复完成 ===\n";

echo "\n🔧 已修复的问题:\n";
foreach ($fixedIssues as $issue) {
    echo "  ✅ $issue\n";
}

if (!empty($errors)) {
    echo "\n❌ 修复失败的问题:\n";
    foreach ($errors as $error) {
        echo "  ✗ $error\n";
    }
}

echo "\n💡 修复总结:\n";
echo "  - 修复了函数重复声明问题\n";
echo "  - 修复了配置文件权限问题\n";
echo "  - 修复了语言文件路径问题\n";
echo "  - 创建了必要的配置文件\n";
echo "  - 添加了安装检查逻辑\n";
echo "  - 创建了必要的目录结构\n";

echo "\n🚀 现在可以重新访问网站了！\n";

?>
