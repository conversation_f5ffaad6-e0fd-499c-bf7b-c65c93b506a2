<?php

echo "=== 修复安全漏洞 ===\n\n";

// 1. 添加CSRF保护函数到框架
echo "1. 添加CSRF保护函数...\n";

$csrfFunctions = '
// CSRF保护函数
function csrf_token_generate() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    $token = bin2hex(random_bytes(32));
    $_SESSION[\'csrf_token\'] = $token;
    return $token;
}

function csrf_token_verify($token) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    return isset($_SESSION[\'csrf_token\']) && hash_equals($_SESSION[\'csrf_token\'], $token);
}

function csrf_token_field() {
    $token = csrf_token_generate();
    return \'<input type="hidden" name="csrf_token" value="\' . htmlspecialchars($token, ENT_QUOTES, \'UTF-8\') . \'">\';
}

function csrf_token_meta() {
    $token = csrf_token_generate();
    return \'<meta name="csrf-token" content="\' . htmlspecialchars($token, ENT_QUOTES, \'UTF-8\') . \'">\';
}
';

// 检查boyouphp.php是否已包含CSRF函数
$boyouphpPath = 'boyouphp/boyouphp.php';
if (file_exists($boyouphpPath)) {
    $content = file_get_contents($boyouphpPath);
    
    if (strpos($content, 'function csrf_token_generate') === false) {
        // 在文件末尾添加CSRF函数
        $content = str_replace('?>', $csrfFunctions . "\n?>", $content);
        
        if (file_put_contents($boyouphpPath, $content)) {
            echo "✓ CSRF保护函数已添加到 boyouphp.php\n";
        } else {
            echo "✗ 添加CSRF保护函数失败\n";
        }
    } else {
        echo "✓ CSRF保护函数已存在\n";
    }
}

// 2. 创建安全的输入过滤函数
echo "\n2. 添加输入过滤函数...\n";

$securityFunctions = '
// 安全输入过滤函数
function xn_input_filter($input, $type = "string") {
    if (is_array($input)) {
        return array_map(function($item) use ($type) {
            return xn_input_filter($item, $type);
        }, $input);
    }
    
    switch ($type) {
        case "int":
            return intval($input);
        case "float":
            return floatval($input);
        case "email":
            return filter_var($input, FILTER_SANITIZE_EMAIL);
        case "url":
            return filter_var($input, FILTER_SANITIZE_URL);
        case "html":
            return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, "UTF-8");
        case "sql":
            return addslashes($input);
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES | ENT_HTML5, "UTF-8");
    }
}

function xn_validate_input($input, $type, $required = false) {
    if ($required && empty($input)) {
        return false;
    }
    
    switch ($type) {
        case "int":
            return filter_var($input, FILTER_VALIDATE_INT) !== false;
        case "float":
            return filter_var($input, FILTER_VALIDATE_FLOAT) !== false;
        case "email":
            return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
        case "url":
            return filter_var($input, FILTER_VALIDATE_URL) !== false;
        case "ip":
            return filter_var($input, FILTER_VALIDATE_IP) !== false;
        default:
            return true;
    }
}

// 安全的文件上传函数
function xn_upload_file($file, $allowedTypes = [], $maxSize = 2097152) {
    if (!isset($file[\'error\']) || is_array($file[\'error\'])) {
        return [\'error\' => \'Invalid file upload\'];
    }
    
    switch ($file[\'error\']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            return [\'error\' => \'No file uploaded\'];
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return [\'error\' => \'File too large\'];
        default:
            return [\'error\' => \'Upload error\'];
    }
    
    if ($file[\'size\'] > $maxSize) {
        return [\'error\' => \'File too large\'];
    }
    
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->file($file[\'tmp_name\']);
    
    if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
        return [\'error\' => \'File type not allowed\'];
    }
    
    $extension = pathinfo($file[\'name\'], PATHINFO_EXTENSION);
    $filename = bin2hex(random_bytes(16)) . \'.\' . $extension;
    
    return [\'success\' => true, \'filename\' => $filename, \'mime_type\' => $mimeType];
}
';

if (file_exists($boyouphpPath)) {
    $content = file_get_contents($boyouphpPath);
    
    if (strpos($content, 'function xn_input_filter') === false) {
        $content = str_replace('?>', $securityFunctions . "\n?>", $content);
        
        if (file_put_contents($boyouphpPath, $content)) {
            echo "✓ 安全过滤函数已添加到 boyouphp.php\n";
        } else {
            echo "✗ 添加安全过滤函数失败\n";
        }
    } else {
        echo "✓ 安全过滤函数已存在\n";
    }
}

// 3. 创建安全配置文件
echo "\n3. 创建安全配置文件...\n";

$securityConfig = '<?php
// 安全配置文件
// 请根据实际需要修改这些设置

return [
    // CSRF保护
    \'csrf_protection\' => true,
    \'csrf_token_name\' => \'csrf_token\',
    \'csrf_expire_time\' => 3600, // 1小时
    
    // XSS保护
    \'xss_protection\' => true,
    \'auto_escape_output\' => true,
    
    // 文件上传安全
    \'upload_max_size\' => 2097152, // 2MB
    \'allowed_upload_types\' => [
        \'image/jpeg\',
        \'image/png\',
        \'image/gif\',
        \'text/plain\'
    ],
    \'upload_path\' => \'./upload/\',
    
    // SQL注入防护
    \'use_prepared_statements\' => true,
    \'auto_escape_sql\' => true,
    
    // 会话安全
    \'session_secure\' => false, // 生产环境请设为true(需要HTTPS)
    \'session_httponly\' => true,
    \'session_samesite\' => \'Strict\',
    
    // 密码安全
    \'password_min_length\' => 8,
    \'password_require_special\' => true,
    \'password_hash_algo\' => PASSWORD_ARGON2ID,
    
    // 访问控制
    \'rate_limit_enabled\' => true,
    \'rate_limit_requests\' => 100, // 每分钟最大请求数
    \'admin_ip_whitelist\' => [], // 管理员IP白名单
    
    // 日志记录
    \'security_log_enabled\' => true,
    \'security_log_file\' => \'./log/security.log\',
    
    // 其他安全设置
    \'disable_error_display\' => true, // 生产环境隐藏错误信息
    \'enable_security_headers\' => true,
];
?>';

if (!file_exists('conf/security.php')) {
    if (file_put_contents('conf/security.php', $securityConfig)) {
        echo "✓ 安全配置文件已创建: conf/security.php\n";
    } else {
        echo "✗ 创建安全配置文件失败\n";
    }
} else {
    echo "✓ 安全配置文件已存在\n";
}

// 4. 创建安全中间件
echo "\n4. 创建安全中间件...\n";

$securityMiddleware = '<?php
// 安全中间件
class SecurityMiddleware {
    private $config;
    
    public function __construct($config = []) {
        $this->config = array_merge([
            \'csrf_protection\' => true,
            \'xss_protection\' => true,
            \'rate_limit_enabled\' => true
        ], $config);
    }
    
    public function handle() {
        // 设置安全头
        $this->setSecurityHeaders();
        
        // CSRF保护
        if ($this->config[\'csrf_protection\'] && $_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
            $this->checkCSRF();
        }
        
        // XSS保护
        if ($this->config[\'xss_protection\']) {
            $this->filterInput();
        }
        
        // 速率限制
        if ($this->config[\'rate_limit_enabled\']) {
            $this->checkRateLimit();
        }
    }
    
    private function setSecurityHeaders() {
        header(\'X-Content-Type-Options: nosniff\');
        header(\'X-Frame-Options: DENY\');
        header(\'X-XSS-Protection: 1; mode=block\');
        header(\'Referrer-Policy: strict-origin-when-cross-origin\');
        
        if (isset($_SERVER[\'HTTPS\']) && $_SERVER[\'HTTPS\'] === \'on\') {
            header(\'Strict-Transport-Security: max-age=31536000; includeSubDomains\');
        }
    }
    
    private function checkCSRF() {
        $token = $_POST[\'csrf_token\'] ?? \'\';
        if (!csrf_token_verify($token)) {
            http_response_code(403);
            die(\'CSRF token validation failed\');
        }
    }
    
    private function filterInput() {
        $_GET = $this->filterArray($_GET);
        $_POST = $this->filterArray($_POST);
        $_REQUEST = $this->filterArray($_REQUEST);
    }
    
    private function filterArray($array) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->filterArray($value);
            } else {
                $array[$key] = xn_input_filter($value);
            }
        }
        return $array;
    }
    
    private function checkRateLimit() {
        $ip = $_SERVER[\'REMOTE_ADDR\'] ?? \'unknown\';
        $key = \'rate_limit_\' . md5($ip);
        
        if (function_exists(\'apcu_fetch\')) {
            $requests = apcu_fetch($key) ?: 0;
            if ($requests > 100) { // 每分钟100次请求
                http_response_code(429);
                die(\'Rate limit exceeded\');
            }
            apcu_store($key, $requests + 1, 60);
        }
    }
}
?>';

if (!file_exists('boyouphp/security_middleware.php')) {
    if (file_put_contents('boyouphp/security_middleware.php', $securityMiddleware)) {
        echo "✓ 安全中间件已创建: boyouphp/security_middleware.php\n";
    } else {
        echo "✗ 创建安全中间件失败\n";
    }
} else {
    echo "✓ 安全中间件已存在\n";
}

// 5. 更新主框架文件以包含安全中间件
echo "\n5. 更新主框架文件...\n";

if (file_exists($boyouphpPath)) {
    $content = file_get_contents($boyouphpPath);
    
    if (strpos($content, 'security_middleware.php') === false) {
        $includeStatement = "
// 包含安全中间件
if(file_exists(BOYOUPHP_PATH.'security_middleware.php')) include BOYOUPHP_PATH.'security_middleware.php';

// 加载安全配置
if(file_exists(APP_PATH.'conf/security.php')) {
    \$security_config = include APP_PATH.'conf/security.php';
    G('security_config', \$security_config);
}
";
        
        $content = str_replace('// hook boyouphp_include_after.php', $includeStatement . "\n// hook boyouphp_include_after.php", $content);
        
        if (file_put_contents($boyouphpPath, $content)) {
            echo "✓ 主框架文件已更新\n";
        } else {
            echo "✗ 更新主框架文件失败\n";
        }
    } else {
        echo "✓ 主框架文件已包含安全组件\n";
    }
}

echo "\n=== 安全漏洞修复完成 ===\n";

echo "\n🛡️ 已修复的安全问题:\n";
echo "  ✅ 添加了CSRF保护机制\n";
echo "  ✅ 添加了输入过滤和验证函数\n";
echo "  ✅ 创建了安全配置文件\n";
echo "  ✅ 添加了安全中间件\n";
echo "  ✅ 设置了安全HTTP头\n";

echo "\n💡 后续建议:\n";
echo "  - 在所有POST表单中添加CSRF令牌\n";
echo "  - 使用xn_input_filter()过滤所有用户输入\n";
echo "  - 配置HTTPS并启用安全会话设置\n";
echo "  - 定期检查和更新安全配置\n";
echo "  - 启用安全日志记录\n";

?>
