<?php

echo "=== 修复XSS漏洞 ===\n\n";

// 需要修复的模板文件
$templateFiles = [
    'admin/view/htm/group_update.htm',
    'admin/view/htm/group_list.htm', 
    'admin/view/htm/user_list.htm',
    'admin/view/htm/user_update.htm',
    'view/htm/user.htm',
    'view/htm/user.template.htm',
    'view/htm/thread_list.inc.htm',
    'view/htm/user.common.template.htm',
    'view/htm/user_thread.template.htm',
    'view/htm/post_list.inc.htm'
];

$fixedFiles = [];
$errorFiles = [];

echo "开始修复XSS漏洞...\n\n";

foreach ($templateFiles as $file) {
    if (!file_exists($file)) {
        echo "跳过不存在的文件: $file\n";
        continue;
    }
    
    echo "处理文件: $file\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // XSS修复规则
    $xssPatterns = [
        // 修复直接输出变量
        '/\{\$([a-zA-Z_][a-zA-Z0-9_]*)\}/' => '{$\\1|htmlspecialchars}',
        '/\{\$([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)\}/' => '{$\\1|htmlspecialchars}',
        '/\{\$([a-zA-Z_][a-zA-Z0-9_]*\[[^\]]+\])\}/' => '{$\\1|htmlspecialchars}',
        
        // 修复PHP直接输出
        '/echo\s+\$([a-zA-Z_][a-zA-Z0-9_]*);/' => 'echo htmlspecialchars($\\1, ENT_QUOTES, \'UTF-8\');',
        '/print\s+\$([a-zA-Z_][a-zA-Z0-9_]*);/' => 'print htmlspecialchars($\\1, ENT_QUOTES, \'UTF-8\');',
        
        // 修复属性值
        '/value="\{\$([a-zA-Z_][a-zA-Z0-9_]*)\}"/' => 'value="{$\\1|htmlspecialchars}"',
        '/title="\{\$([a-zA-Z_][a-zA-Z0-9_]*)\}"/' => 'title="{$\\1|htmlspecialchars}"',
        '/alt="\{\$([a-zA-Z_][a-zA-Z0-9_]*)\}"/' => 'alt="{$\\1|htmlspecialchars}"',
    ];
    
    $changes = 0;
    foreach ($xssPatterns as $pattern => $replacement) {
        $newContent = preg_replace($pattern, $replacement, $content);
        if ($newContent !== $content) {
            $count = preg_match_all($pattern, $content);
            $changes += $count;
            $content = $newContent;
        }
    }
    
    // 特殊处理：确保已经转义的不重复转义
    $content = str_replace('|htmlspecialchars|htmlspecialchars', '|htmlspecialchars', $content);
    
    // 如果有更改，保存文件
    if ($content !== $originalContent) {
        if (file_put_contents($file, $content)) {
            echo "  ✓ 修复了 $changes 处XSS漏洞\n";
            $fixedFiles[] = $file;
        } else {
            echo "  ✗ 保存文件失败\n";
            $errorFiles[] = $file;
        }
    } else {
        echo "  - 无需修复\n";
    }
}

// 创建安全的模板函数
echo "\n创建安全模板函数...\n";

$templateSecurityFunctions = '<?php
// 模板安全函数

// 安全输出函数
function safe_echo($value, $escape = true) {
    if ($escape) {
        echo htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, \'UTF-8\');
    } else {
        echo $value;
    }
}

// 安全属性输出
function safe_attr($value) {
    return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, \'UTF-8\');
}

// 安全URL输出
function safe_url($url) {
    return htmlspecialchars(filter_var($url, FILTER_SANITIZE_URL), ENT_QUOTES | ENT_HTML5, \'UTF-8\');
}

// 安全JavaScript输出
function safe_js($value) {
    return json_encode($value, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}

// 安全CSS输出
function safe_css($value) {
    // 移除潜在危险的CSS
    $value = preg_replace(\'/[^a-zA-Z0-9\\s\\-_#.,;:()%]/\', \'\', $value);
    return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, \'UTF-8\');
}

// 富文本安全输出（允许部分HTML标签）
function safe_html($html, $allowedTags = \'<p><br><strong><em><u><a><img><ul><ol><li>\') {
    // 使用strip_tags保留允许的标签
    $html = strip_tags($html, $allowedTags);
    
    // 进一步过滤属性
    $html = preg_replace(\'/(<a[^>]*href=")([^"]*)/i\', \'$1\' . safe_url(\'$2\'), $html);
    $html = preg_replace(\'/(<img[^>]*src=")([^"]*)/i\', \'$1\' . safe_url(\'$2\'), $html);
    
    return $html;
}

// 模板变量过滤器
function template_filter($value, $filter = \'html\') {
    switch ($filter) {
        case \'html\':
            return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, \'UTF-8\');
        case \'attr\':
            return safe_attr($value);
        case \'url\':
            return safe_url($value);
        case \'js\':
            return safe_js($value);
        case \'css\':
            return safe_css($value);
        case \'rich\':
            return safe_html($value);
        case \'raw\':
            return $value; // 不过滤，谨慎使用
        default:
            return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, \'UTF-8\');
    }
}
?>';

if (!file_exists('boyouphp/template_security.php')) {
    if (file_put_contents('boyouphp/template_security.php', $templateSecurityFunctions)) {
        echo "✓ 模板安全函数已创建: boyouphp/template_security.php\n";
    } else {
        echo "✗ 创建模板安全函数失败\n";
    }
}

// 更新框架以包含模板安全函数
echo "\n更新框架文件...\n";

$boyouphpPath = 'boyouphp/boyouphp.php';
if (file_exists($boyouphpPath)) {
    $content = file_get_contents($boyouphpPath);
    
    if (strpos($content, 'template_security.php') === false) {
        $includeStatement = "
// 包含模板安全函数
if(file_exists(BOYOUPHP_PATH.'template_security.php')) include BOYOUPHP_PATH.'template_security.php';
";
        
        $content = str_replace('// hook boyouphp_include_after.php', $includeStatement . "\n// hook boyouphp_include_after.php", $content);
        
        if (file_put_contents($boyouphpPath, $content)) {
            echo "✓ 框架文件已更新以包含模板安全函数\n";
        } else {
            echo "✗ 更新框架文件失败\n";
        }
    } else {
        echo "✓ 框架文件已包含模板安全函数\n";
    }
}

// 创建XSS防护使用指南
echo "\n创建XSS防护使用指南...\n";

$xssGuide = '# XSS防护使用指南

## 模板中的安全输出

### 1. 基本变量输出
```html
<!-- 不安全 -->
{$username}

<!-- 安全 -->
{$username|htmlspecialchars}
或
<?php echo safe_echo($username); ?>
```

### 2. 属性值输出
```html
<!-- 不安全 -->
<input value="{$value}">

<!-- 安全 -->
<input value="{$value|htmlspecialchars}">
或
<input value="<?php echo safe_attr($value); ?>">
```

### 3. URL输出
```html
<!-- 不安全 -->
<a href="{$url}">链接</a>

<!-- 安全 -->
<a href="<?php echo safe_url($url); ?>">链接</a>
```

### 4. JavaScript中的变量
```html
<!-- 不安全 -->
<script>var data = "{$data}";</script>

<!-- 安全 -->
<script>var data = <?php echo safe_js($data); ?>;</script>
```

### 5. 富文本内容
```html
<!-- 不安全 -->
{$content}

<!-- 安全（允许部分HTML） -->
<?php echo safe_html($content); ?>

<!-- 安全（完全转义） -->
<?php echo safe_echo($content); ?>
```

## PHP代码中的安全输出

### 1. 直接输出
```php
// 不安全
echo $_GET[\'name\'];

// 安全
echo htmlspecialchars($_GET[\'name\'], ENT_QUOTES | ENT_HTML5, \'UTF-8\');
// 或
echo xn_input_filter($_GET[\'name\']);
```

### 2. 使用过滤器
```php
// 过滤不同类型的输入
$username = xn_input_filter($_POST[\'username\'], \'string\');
$email = xn_input_filter($_POST[\'email\'], \'email\');
$age = xn_input_filter($_POST[\'age\'], \'int\');
```

## 安全函数说明

- `safe_echo($value)` - 安全输出文本
- `safe_attr($value)` - 安全输出HTML属性值
- `safe_url($url)` - 安全输出URL
- `safe_js($value)` - 安全输出到JavaScript
- `safe_css($value)` - 安全输出CSS值
- `safe_html($html)` - 安全输出富文本（允许部分HTML标签）
- `xn_input_filter($input, $type)` - 输入过滤和验证

## 注意事项

1. 默认情况下，所有用户输入都应该被转义
2. 只有在确实需要输出HTML时才使用 `safe_html()`
3. 在JavaScript中输出变量时必须使用 `safe_js()`
4. URL必须经过验证和过滤
5. 定期检查和更新安全过滤规则
';

if (file_put_contents('XSS_PROTECTION_GUIDE.md', $xssGuide)) {
    echo "✓ XSS防护使用指南已创建: XSS_PROTECTION_GUIDE.md\n";
}

echo "\n=== XSS漏洞修复完成 ===\n";

echo "\n📊 修复统计:\n";
echo "  修复文件数: " . count($fixedFiles) . "\n";
echo "  失败文件数: " . count($errorFiles) . "\n";

if (!empty($fixedFiles)) {
    echo "\n✅ 已修复的文件:\n";
    foreach ($fixedFiles as $file) {
        echo "  - $file\n";
    }
}

if (!empty($errorFiles)) {
    echo "\n❌ 修复失败的文件:\n";
    foreach ($errorFiles as $file) {
        echo "  - $file\n";
    }
}

echo "\n🛡️ XSS防护措施:\n";
echo "  ✅ 添加了模板安全函数\n";
echo "  ✅ 修复了模板文件中的XSS漏洞\n";
echo "  ✅ 创建了安全输出函数\n";
echo "  ✅ 提供了使用指南\n";

echo "\n💡 后续建议:\n";
echo "  - 在所有模板中使用安全输出函数\n";
echo "  - 定期检查新增的模板文件\n";
echo "  - 培训开发人员XSS防护知识\n";
echo "  - 使用自动化工具检测XSS漏洞\n";

?>
