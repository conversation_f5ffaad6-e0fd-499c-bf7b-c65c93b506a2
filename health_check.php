<?php
// 系统健康检查脚本
header('Content-Type: application/json');

$health = [
    'status' => 'ok',
    'timestamp' => date('c'),
    'checks' => []
];

// 检查数据库连接
try {
    if (file_exists('./conf/conf.php')) {
        $conf = include './conf/conf.php';
        if (isset($conf['db'])) {
            // 这里应该实际测试数据库连接
            $health['checks']['database'] = 'ok';
        } else {
            $health['checks']['database'] = 'config_missing';
            $health['status'] = 'warning';
        }
    } else {
        $health['checks']['database'] = 'config_file_missing';
        $health['status'] = 'error';
    }
} catch (Exception $e) {
    $health['checks']['database'] = 'error';
    $health['status'] = 'error';
}

// 检查文件系统
$writableDirs = ['upload', 'log', 'tmp'];
foreach ($writableDirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        $health['checks']['filesystem_' . $dir] = 'ok';
    } else {
        $health['checks']['filesystem_' . $dir] = 'not_writable';
        $health['status'] = 'warning';
    }
}

// 检查PHP扩展
$requiredExtensions = ['pdo', 'json', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $health['checks']['extension_' . $ext] = 'ok';
    } else {
        $health['checks']['extension_' . $ext] = 'missing';
        $health['status'] = 'error';
    }
}

// 检查内存使用
$memoryUsage = memory_get_usage(true);
$memoryLimit = ini_get('memory_limit');
$memoryLimitBytes = return_bytes($memoryLimit);
$memoryPercent = ($memoryUsage / $memoryLimitBytes) * 100;

if ($memoryPercent > 80) {
    $health['checks']['memory'] = 'high_usage';
    $health['status'] = 'warning';
} else {
    $health['checks']['memory'] = 'ok';
}

$health['memory_usage'] = round($memoryUsage / 1024 / 1024, 2) . 'MB';
$health['memory_limit'] = $memoryLimit;

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }
    return $val;
}

echo json_encode($health, JSON_PRETTY_PRINT);
?>