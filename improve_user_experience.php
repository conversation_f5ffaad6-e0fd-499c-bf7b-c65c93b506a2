<?php

echo "=== Boyou BBS 6.1 用户体验优化工具 ===\n\n";

class UserExperienceImprover {
    private $improvements = [];
    
    public function runAllImprovements() {
        echo "开始用户体验优化...\n\n";
        
        $this->improveErrorMessages();
        $this->optimizePageLoading();
        $this->enhanceFormValidation();
        $this->improveNavigation();
        $this->addUserFeedback();
        $this->optimizeMobileExperience();
        $this->improveAccessibility();
        
        $this->generateImprovementReport();
    }
    
    private function improveErrorMessages() {
        echo "1. 优化错误提示信息...\n";
        
        // 创建友好的错误消息配置
        $errorMessages = [
            'user_not_found' => '用户不存在，请检查用户名或邮箱地址',
            'password_incorrect' => '密码错误，请重新输入',
            'email_exists' => '该邮箱已被注册，请使用其他邮箱或尝试找回密码',
            'username_exists' => '用户名已被占用，请选择其他用户名',
            'password_too_weak' => '密码强度不够，请包含大小写字母、数字和特殊字符',
            'email_invalid' => '邮箱格式不正确，请输入有效的邮箱地址',
            'csrf_token_invalid' => '安全验证失败，请刷新页面后重试',
            'permission_denied' => '您没有权限执行此操作',
            'file_too_large' => '文件大小超过限制，请选择较小的文件',
            'file_type_not_allowed' => '不支持的文件类型，请选择图片或文档文件'
        ];
        
        file_put_contents('lang/zh-cn/error_messages.php', 
            "<?php\nreturn " . var_export($errorMessages, true) . ";\n?>");
        
        echo "  ✓ 错误消息配置已创建\n";
        $this->addImprovement('错误提示', '创建友好的错误消息配置', true);
        
        // 创建成功消息配置
        $successMessages = [
            'user_registered' => '注册成功！欢迎加入 Boyou BBS',
            'login_success' => '登录成功！欢迎回来',
            'password_changed' => '密码修改成功',
            'profile_updated' => '个人资料更新成功',
            'post_created' => '帖子发布成功',
            'post_updated' => '帖子更新成功',
            'email_sent' => '邮件发送成功，请查收',
            'file_uploaded' => '文件上传成功'
        ];
        
        file_put_contents('lang/zh-cn/success_messages.php', 
            "<?php\nreturn " . var_export($successMessages, true) . ";\n?>");
        
        echo "  ✓ 成功消息配置已创建\n";
        echo "\n";
    }
    
    private function optimizePageLoading() {
        echo "2. 优化页面加载性能...\n";
        
        // 创建资源压缩配置
        $optimizationConfig = [
            'css_minify' => true,
            'js_minify' => true,
            'html_compress' => true,
            'image_lazy_load' => true,
            'cache_static_files' => true,
            'gzip_compression' => true
        ];
        
        file_put_contents('conf/optimization.php', 
            "<?php\nreturn " . var_export($optimizationConfig, true) . ";\n?>");
        
        echo "  ✓ 性能优化配置已创建\n";
        
        // 创建加载动画CSS
        $loadingCSS = '
/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.page-loading {
    text-align: center;
}

.page-loading .loading-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
}
';
        
        file_put_contents('view/css/loading.css', $loadingCSS);
        echo "  ✓ 加载动画样式已创建\n";
        
        $this->addImprovement('页面加载', '优化加载性能和用户反馈', true);
        echo "\n";
    }
    
    private function enhanceFormValidation() {
        echo "3. 增强表单验证...\n";
        
        // 创建前端验证JavaScript
        $validationJS = '
// 表单验证增强
(function() {
    "use strict";
    
    // 实时密码强度检查
    function checkPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        return strength;
    }
    
    // 显示密码强度
    function showPasswordStrength(input) {
        const strength = checkPasswordStrength(input.value);
        const indicator = input.parentNode.querySelector(".password-strength");
        
        if (indicator) {
            const levels = ["很弱", "弱", "一般", "强", "很强"];
            const colors = ["#ff4757", "#ff6b7a", "#ffa502", "#2ed573", "#1e90ff"];
            
            indicator.textContent = "密码强度: " + (levels[strength - 1] || "很弱");
            indicator.style.color = colors[strength - 1] || colors[0];
        }
    }
    
    // 邮箱格式验证
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // 用户名验证
    function validateUsername(username) {
        return /^[a-zA-Z0-9_]{3,20}$/.test(username);
    }
    
    // 初始化表单验证
    document.addEventListener("DOMContentLoaded", function() {
        // 密码强度检查
        const passwordInputs = document.querySelectorAll("input[type=password]");
        passwordInputs.forEach(function(input) {
            if (input.name === "password" || input.name === "new_password") {
                const strengthDiv = document.createElement("div");
                strengthDiv.className = "password-strength";
                input.parentNode.appendChild(strengthDiv);
                
                input.addEventListener("input", function() {
                    showPasswordStrength(this);
                });
            }
        });
        
        // 邮箱验证
        const emailInputs = document.querySelectorAll("input[type=email]");
        emailInputs.forEach(function(input) {
            input.addEventListener("blur", function() {
                if (this.value && !validateEmail(this.value)) {
                    this.setCustomValidity("请输入有效的邮箱地址");
                } else {
                    this.setCustomValidity("");
                }
            });
        });
        
        // 用户名验证
        const usernameInputs = document.querySelectorAll("input[name=username]");
        usernameInputs.forEach(function(input) {
            input.addEventListener("blur", function() {
                if (this.value && !validateUsername(this.value)) {
                    this.setCustomValidity("用户名只能包含字母、数字和下划线，长度3-20位");
                } else {
                    this.setCustomValidity("");
                }
            });
        });
    });
})();
';
        
        file_put_contents('view/js/form-validation.js', $validationJS);
        echo "  ✓ 表单验证脚本已创建\n";
        
        $this->addImprovement('表单验证', '增强前端表单验证和用户反馈', true);
        echo "\n";
    }
    
    private function improveNavigation() {
        echo "4. 改进导航体验...\n";
        
        // 创建面包屑导航CSS
        $breadcrumbCSS = '
/* 面包屑导航 */
.breadcrumb {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.breadcrumb-item {
    display: inline-block;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: " > ";
    color: #6c757d;
    margin: 0 5px;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    display: none;
    z-index: 1000;
    transition: all 0.3s;
}

.back-to-top:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.back-to-top.show {
    display: block;
}
';
        
        file_put_contents('view/css/navigation.css', $breadcrumbCSS);
        echo "  ✓ 导航样式已创建\n";
        
        // 创建导航增强JavaScript
        $navigationJS = '
// 导航增强
(function() {
    "use strict";
    
    // 返回顶部功能
    function initBackToTop() {
        const backToTopBtn = document.createElement("button");
        backToTopBtn.className = "back-to-top";
        backToTopBtn.innerHTML = "↑";
        backToTopBtn.title = "返回顶部";
        document.body.appendChild(backToTopBtn);
        
        window.addEventListener("scroll", function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add("show");
            } else {
                backToTopBtn.classList.remove("show");
            }
        });
        
        backToTopBtn.addEventListener("click", function() {
            window.scrollTo({
                top: 0,
                behavior: "smooth"
            });
        });
    }
    
    // 活动导航项高亮
    function highlightActiveNav() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll("nav a, .nav a");
        
        navLinks.forEach(function(link) {
            if (link.getAttribute("href") === currentPath) {
                link.classList.add("active");
            }
        });
    }
    
    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        initBackToTop();
        highlightActiveNav();
    });
})();
';
        
        file_put_contents('view/js/navigation.js', $navigationJS);
        echo "  ✓ 导航脚本已创建\n";
        
        $this->addImprovement('导航体验', '添加面包屑导航和返回顶部功能', true);
        echo "\n";
    }
    
    private function addUserFeedback() {
        echo "5. 添加用户反馈机制...\n";
        
        // 创建通知系统CSS
        $notificationCSS = '
/* 通知系统 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    z-index: 10000;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.success {
    background: #28a745;
}

.notification.error {
    background: #dc3545;
}

.notification.warning {
    background: #ffc107;
    color: #212529;
}

.notification.info {
    background: #17a2b8;
}

.notification .close {
    float: right;
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    margin-left: 10px;
}

/* 加载状态 */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
';
        
        file_put_contents('view/css/notifications.css', $notificationCSS);
        echo "  ✓ 通知系统样式已创建\n";
        
        // 创建通知系统JavaScript
        $notificationJS = '
// 通知系统
window.Notification = (function() {
    "use strict";
    
    function show(message, type, duration) {
        type = type || "info";
        duration = duration || 5000;
        
        const notification = document.createElement("div");
        notification.className = "notification " + type;
        notification.innerHTML = message + "<button class=\"close\">&times;</button>";
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(function() {
            notification.classList.add("show");
        }, 100);
        
        // 关闭按钮
        notification.querySelector(".close").addEventListener("click", function() {
            hide(notification);
        });
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(function() {
                hide(notification);
            }, duration);
        }
        
        return notification;
    }
    
    function hide(notification) {
        notification.classList.remove("show");
        setTimeout(function() {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    return {
        success: function(message, duration) {
            return show(message, "success", duration);
        },
        error: function(message, duration) {
            return show(message, "error", duration);
        },
        warning: function(message, duration) {
            return show(message, "warning", duration);
        },
        info: function(message, duration) {
            return show(message, "info", duration);
        }
    };
})();

// 按钮加载状态
window.ButtonLoader = {
    start: function(button) {
        button.classList.add("loading");
        button.disabled = true;
    },
    stop: function(button) {
        button.classList.remove("loading");
        button.disabled = false;
    }
};
';
        
        file_put_contents('view/js/notifications.js', $notificationJS);
        echo "  ✓ 通知系统脚本已创建\n";
        
        $this->addImprovement('用户反馈', '添加通知系统和加载状态反馈', true);
        echo "\n";
    }
    
    private function optimizeMobileExperience() {
        echo "6. 优化移动端体验...\n";
        
        // 创建移动端优化CSS
        $mobileCSS = '
/* 移动端优化 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    input, textarea, select {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .table-responsive {
        border: none;
    }
    
    .navbar-collapse {
        background: white;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* 触摸友好 */
.touch-friendly {
    min-height: 44px;
    min-width: 44px;
}

/* 手势支持 */
.swipe-item {
    touch-action: pan-y;
}
';
        
        file_put_contents('view/css/mobile.css', $mobileCSS);
        echo "  ✓ 移动端样式已创建\n";
        
        $this->addImprovement('移动端体验', '优化移动设备显示和交互', true);
        echo "\n";
    }
    
    private function improveAccessibility() {
        echo "7. 改进无障碍访问...\n";
        
        // 创建无障碍CSS
        $accessibilityCSS = '
/* 无障碍访问 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }
    
    a {
        text-decoration: underline;
    }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 焦点样式 */
:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}
';
        
        file_put_contents('view/css/accessibility.css', $accessibilityCSS);
        echo "  ✓ 无障碍样式已创建\n";
        
        $this->addImprovement('无障碍访问', '改进键盘导航和屏幕阅读器支持', true);
        echo "\n";
    }
    
    private function addImprovement($category, $description, $success) {
        $this->improvements[] = [
            'category' => $category,
            'description' => $description,
            'success' => $success
        ];
    }
    
    private function generateImprovementReport() {
        echo "=== 用户体验优化报告 ===\n\n";
        
        $total = count($this->improvements);
        $successful = array_filter($this->improvements, function($improvement) {
            return $improvement['success'];
        });
        $successCount = count($successful);
        
        echo "📊 优化统计:\n";
        echo "  总优化项: $total\n";
        echo "  成功优化: $successCount\n";
        echo "  成功率: " . round(($successCount / $total) * 100, 1) . "%\n\n";
        
        echo "✅ 已完成的优化:\n";
        foreach ($this->improvements as $improvement) {
            if ($improvement['success']) {
                echo "  • {$improvement['category']}: {$improvement['description']}\n";
            }
        }
        
        echo "\n📁 创建的文件:\n";
        $files = [
            'lang/zh-cn/error_messages.php' => '错误消息配置',
            'lang/zh-cn/success_messages.php' => '成功消息配置',
            'conf/optimization.php' => '性能优化配置',
            'view/css/loading.css' => '加载动画样式',
            'view/js/form-validation.js' => '表单验证脚本',
            'view/css/navigation.css' => '导航样式',
            'view/js/navigation.js' => '导航脚本',
            'view/css/notifications.css' => '通知系统样式',
            'view/js/notifications.js' => '通知系统脚本',
            'view/css/mobile.css' => '移动端样式',
            'view/css/accessibility.css' => '无障碍样式'
        ];
        
        foreach ($files as $file => $description) {
            echo "  • $file - $description\n";
        }
        
        echo "\n💡 使用建议:\n";
        echo "1. 在模板文件中引入新创建的CSS和JS文件\n";
        echo "2. 测试各项功能在不同设备上的表现\n";
        echo "3. 收集用户反馈并持续改进\n";
        echo "4. 定期检查无障碍访问性\n";
        echo "5. 监控页面加载性能\n";
        
        // 保存优化报告
        $reportData = [
            'optimization_time' => date('Y-m-d H:i:s'),
            'total_improvements' => $total,
            'successful_improvements' => $successCount,
            'success_rate' => round(($successCount / $total) * 100, 1),
            'improvements' => $this->improvements,
            'files_created' => array_keys($files)
        ];
        
        file_put_contents('user_experience_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n📄 优化报告已保存到: user_experience_report.json\n";
    }
}

// 主程序
$improver = new UserExperienceImprover();
$improver->runAllImprovements();

echo "\n=== 用户体验优化完成 ===\n";

?>
