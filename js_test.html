<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS JavaScript 功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { margin: 5px; padding: 8px 16px; }
    </style>
</head>
<body>
    <h1>Boyou BBS 6.1 JavaScript 功能测试</h1>
    
    <div id="test-results"></div>
    
    <h2>手动测试</h2>
    <button onclick="testBasicFunctions()">测试基本函数</button>
    <button onclick="testStringFunctions()">测试字符串函数</button>
    <button onclick="testArrayFunctions()">测试数组函数</button>
    <button onclick="testUtilityFunctions()">测试工具函数</button>
    <button onclick="runAllTests()">运行所有测试</button>
    
    <script src="js/boyou.js"></script>
    <script>
        function addResult(message, type = "info") {
            const div = document.createElement("div");
            div.className = "test-result " + type;
            div.textContent = message;
            document.getElementById("test-results").appendChild(div);
        }
        
        function testBasicFunctions() {
            addResult("=== 测试基本函数 ===", "info");
            
            try {
                // 测试时间函数
                const time = xn.time();
                if (time > 0) {
                    addResult("✓ xn.time() 工作正常: " + time, "success");
                } else {
                    addResult("✗ xn.time() 工作异常", "error");
                }
                
                // 测试JSON函数
                const obj = {name: "Boyou BBS", version: "6.1"};
                const json = xn.json_encode(obj);
                const decoded = xn.json_decode(json);
                
                if (decoded.name === "Boyou BBS") {
                    addResult("✓ JSON编解码函数工作正常", "success");
                } else {
                    addResult("✗ JSON编解码函数工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 基本函数测试出错: " + e.message, "error");
            }
        }
        
        function testStringFunctions() {
            addResult("=== 测试字符串函数 ===", "info");
            
            try {
                // 测试字符串函数
                const str = "Hello Boyou BBS";
                
                if (xn.strlen(str) === 15) {
                    addResult("✓ xn.strlen() 工作正常", "success");
                } else {
                    addResult("✗ xn.strlen() 工作异常", "error");
                }
                
                if (xn.strtolower(str) === "hello boyou bbs") {
                    addResult("✓ xn.strtolower() 工作正常", "success");
                } else {
                    addResult("✗ xn.strtolower() 工作异常", "error");
                }
                
                if (xn.strtoupper(str) === "HELLO BOYOU BBS") {
                    addResult("✓ xn.strtoupper() 工作正常", "success");
                } else {
                    addResult("✗ xn.strtoupper() 工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 字符串函数测试出错: " + e.message, "error");
            }
        }
        
        function testArrayFunctions() {
            addResult("=== 测试数组函数 ===", "info");
            
            try {
                const arr1 = [1, 2, 3];
                const arr2 = [4, 5, 6];
                const merged = xn.array_merge(arr1, arr2);
                
                if (merged.length === 6 && merged[5] === 6) {
                    addResult("✓ xn.array_merge() 工作正常", "success");
                } else {
                    addResult("✗ xn.array_merge() 工作异常", "error");
                }
                
                const obj = {a: 1, b: 2, c: 3};
                const keys = xn.array_keys(obj);
                const values = xn.array_values(obj);
                
                if (keys.length === 3 && values.length === 3) {
                    addResult("✓ xn.array_keys() 和 xn.array_values() 工作正常", "success");
                } else {
                    addResult("✗ 数组键值函数工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 数组函数测试出错: " + e.message, "error");
            }
        }
        
        function testUtilityFunctions() {
            addResult("=== 测试工具函数 ===", "info");
            
            try {
                // 测试数值函数
                if (xn.intval("123") === 123) {
                    addResult("✓ xn.intval() 工作正常", "success");
                } else {
                    addResult("✗ xn.intval() 工作异常", "error");
                }
                
                if (xn.floatval("123.45") === 123.45) {
                    addResult("✓ xn.floatval() 工作正常", "success");
                } else {
                    addResult("✗ xn.floatval() 工作异常", "error");
                }
                
                // 测试类型检查函数
                if (xn.is_array([1,2,3]) && !xn.is_array("string")) {
                    addResult("✓ xn.is_array() 工作正常", "success");
                } else {
                    addResult("✗ xn.is_array() 工作异常", "error");
                }
                
                if (xn.is_string("test") && !xn.is_string(123)) {
                    addResult("✓ xn.is_string() 工作正常", "success");
                } else {
                    addResult("✗ xn.is_string() 工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 工具函数测试出错: " + e.message, "error");
            }
        }
        
        function runAllTests() {
            document.getElementById("test-results").innerHTML = "";
            addResult("开始运行所有JavaScript测试...", "info");
            
            testBasicFunctions();
            testStringFunctions();
            testArrayFunctions();
            testUtilityFunctions();
            
            addResult("所有测试完成！", "info");
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            addResult("Boyou BBS JavaScript 测试页面已加载", "info");
            if (typeof xn !== "undefined") {
                addResult("✓ boyou.js 已成功加载", "success");
            } else {
                addResult("✗ boyou.js 加载失败", "error");
            }
        };
    </script>
</body>
</html>