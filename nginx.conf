# Boyou BBS 6.1 Nginx 配置文件
# 适用于 Nginx Web服务器

server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;
    
    # 如果使用HTTPS，取消注释以下行并配置SSL证书
    # listen 443 ssl http2;
    # listen [::]:443 ssl http2;
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    root /path/to/boyou-bbs;
    index index.php index.html;
    
    # 字符集
    charset utf-8;
    
    # 安全头部
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 管理后台
    location /admin/ {
        try_files $uri $uri/ /admin/index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        # 只允许特定的PHP文件
        if ($uri !~ ^/(index\.php|admin/index\.php)$) {
            return 403;
        }
        
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;  # 根据PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全设置
        fastcgi_param HTTP_PROXY "";
        fastcgi_read_timeout 300;
    }
    
    # 保护敏感文件
    location ~ /\.(ht|git|svn) {
        deny all;
        return 404;
    }
    
    # 保护配置文件
    location ~ \.(conf|sql|db|log|inc|bak|backup|old|orig|save|swp|tmp)$ {
        deny all;
        return 404;
    }
    
    # 保护敏感目录
    location ~ ^/(conf|data|log|tmp)/ {
        deny all;
        return 404;
    }
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff always;
    }
    
    # 上传目录安全
    location /upload/ {
        # 禁止执行PHP
        location ~ \.php$ {
            deny all;
            return 404;
        }
        
        # 只允许特定文件类型
        location ~* \.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$ {
            add_header X-Content-Type-Options nosniff always;
        }
        
        # 其他文件类型拒绝访问
        location ~* ^(?!.*\.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$).*$ {
            deny all;
            return 404;
        }
    }
    
    # 限制请求方法
    if ($request_method !~ ^(GET|POST|HEAD)$) {
        return 405;
    }
    
    # 防止访问临时文件
    location ~ /tmp/.*\.php$ {
        deny all;
        return 404;
    }
    
    # 错误页面
    error_page 403 /error/403.html;
    error_page 404 /error/404.html;
    error_page 500 502 503 504 /error/500.html;
    
    # 日志配置
    access_log /var/log/nginx/boyou-bbs-access.log;
    error_log /var/log/nginx/boyou-bbs-error.log;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 客户端缓存
    location ~* \.(html|htm)$ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}

# HTTPS重定向 (如果使用HTTPS)
# server {
#     listen 80;
#     listen [::]:80;
#     server_name your-domain.com www.your-domain.com;
#     return 301 https://$server_name$request_uri;
# }
