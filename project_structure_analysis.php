<?php

echo "=== Boyou BBS 6.1 项目结构深度分析 ===\n\n";

class ProjectStructureAnalyzer {
    private $projectRoot;
    private $analysis = [];
    
    public function __construct($projectRoot = '.') {
        $this->projectRoot = $projectRoot;
    }
    
    public function runCompleteAnalysis() {
        echo "开始项目结构深度分析...\n\n";
        
        $this->analyzeDirectoryStructure();
        $this->analyzeCoreComponents();
        $this->analyzeArchitecturePattern();
        $this->analyzeConfigurationSystem();
        $this->analyzeDatabaseDesign();
        $this->analyzeSecurityImplementation();
        $this->analyzeRoutingSystem();
        $this->analyzeTemplateSystem();
        
        $this->generateStructureReport();
    }
    
    private function analyzeDirectoryStructure() {
        echo "1. 分析目录结构...\n";
        
        $structure = [
            '/' => '项目根目录',
            '/admin/' => '管理后台目录',
            '/admin/route/' => '管理后台路由',
            '/admin/view/' => '管理后台模板',
            '/boyouphp/' => 'BoyouPHP框架核心',
            '/conf/' => '配置文件目录',
            '/data/' => '数据存储目录',
            '/install/' => '安装程序目录',
            '/lang/' => '多语言文件',
            '/log/' => '日志文件目录',
            '/model/' => '数据模型目录',
            '/plugin/' => '插件目录',
            '/route/' => '前台路由目录',
            '/tmp/' => '临时文件目录',
            '/tool/' => '工具脚本目录',
            '/upload/' => '上传文件目录',
            '/view/' => '前台模板目录',
            '/view/css/' => '样式文件',
            '/view/js/' => 'JavaScript文件',
            '/view/htm/' => 'HTML模板文件',
            '/view/img/' => '图片资源'
        ];
        
        foreach ($structure as $dir => $description) {
            $fullPath = $this->projectRoot . $dir;
            if (is_dir($fullPath)) {
                $fileCount = $this->countFiles($fullPath);
                echo "  ✓ $dir - $description (包含 $fileCount 个文件)\n";
                $this->analysis['directories'][$dir] = [
                    'exists' => true,
                    'description' => $description,
                    'file_count' => $fileCount
                ];
            } else {
                echo "  ✗ $dir - $description (目录不存在)\n";
                $this->analysis['directories'][$dir] = [
                    'exists' => false,
                    'description' => $description,
                    'file_count' => 0
                ];
            }
        }
        echo "\n";
    }
    
    private function analyzeCoreComponents() {
        echo "2. 分析核心组件...\n";
        
        $coreFiles = [
            'index.php' => '主入口文件 - 处理所有前台请求',
            'index.inc.php' => '路由分发器 - 根据请求分发到对应路由',
            'model.inc.php' => '模型加载器 - 加载所有数据模型',
            'boyouphp/boyouphp.php' => '框架核心 - 提供基础功能和工具函数',
            'boyouphp/boyouphp.min.php' => '框架核心压缩版 - 生产环境使用',
            'admin/index.php' => '管理后台入口 - 处理管理后台请求',
            'admin/admin.func.php' => '管理后台函数库 - 管理相关功能函数'
        ];
        
        foreach ($coreFiles as $file => $description) {
            if (file_exists($file)) {
                $size = filesize($file);
                $lines = count(file($file));
                echo "  ✓ $file - $description\n";
                echo "    文件大小: " . $this->formatBytes($size) . ", 代码行数: $lines\n";
                $this->analysis['core_files'][$file] = [
                    'exists' => true,
                    'description' => $description,
                    'size' => $size,
                    'lines' => $lines
                ];
            } else {
                echo "  ✗ $file - $description (文件不存在)\n";
                $this->analysis['core_files'][$file] = [
                    'exists' => false,
                    'description' => $description
                ];
            }
        }
        echo "\n";
    }
    
    private function analyzeArchitecturePattern() {
        echo "3. 分析架构模式...\n";
        
        // 分析MVC模式实现
        echo "  架构模式: MVC (Model-View-Controller)\n";
        echo "  - Model: /model/ 目录包含数据模型\n";
        echo "  - View: /view/ 目录包含模板文件\n";
        echo "  - Controller: /route/ 目录包含控制器逻辑\n\n";
        
        // 分析路由系统
        echo "  路由系统:\n";
        $routes = glob('route/*.php');
        foreach ($routes as $route) {
            $routeName = basename($route, '.php');
            echo "    - $routeName: 处理 /$routeName 相关请求\n";
        }
        echo "\n";
        
        // 分析模型系统
        echo "  模型系统:\n";
        $models = glob('model/*.func.php');
        foreach ($models as $model) {
            $modelName = basename($model, '.func.php');
            echo "    - $modelName: 提供 $modelName 相关数据操作\n";
        }
        echo "\n";
        
        $this->analysis['architecture'] = [
            'pattern' => 'MVC',
            'routes' => array_map(function($r) { return basename($r, '.php'); }, $routes),
            'models' => array_map(function($m) { return basename($m, '.func.php'); }, $models)
        ];
    }
    
    private function analyzeConfigurationSystem() {
        echo "4. 分析配置系统...\n";
        
        $configFiles = [
            'conf/conf.php' => '主配置文件',
            'conf/conf.default.php' => '默认配置模板',
            'conf/security.php' => '安全配置',
            'conf/smtp.conf.php' => 'SMTP邮件配置',
            'conf/attach.conf.php' => '附件配置'
        ];
        
        foreach ($configFiles as $file => $description) {
            if (file_exists($file)) {
                echo "  ✓ $file - $description\n";
                
                if ($file === 'conf/conf.php') {
                    $conf = include $file;
                    if (is_array($conf)) {
                        echo "    配置项数量: " . count($conf, COUNT_RECURSIVE) . "\n";
                        echo "    主要配置: sitename, siteurl, db, auth_key\n";
                    }
                }
            } else {
                echo "  ✗ $file - $description (文件不存在)\n";
            }
        }
        echo "\n";
    }
    
    private function analyzeDatabaseDesign() {
        echo "5. 分析数据库设计...\n";
        
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 获取所有表
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "  数据库类型: SQLite\n";
            echo "  数据库文件: data/boyou_bbs.db\n";
            echo "  表数量: " . count($tables) . "\n\n";
            
            foreach ($tables as $table) {
                echo "  表: $table\n";
                
                // 获取表结构
                $stmt = $pdo->query("PRAGMA table_info($table)");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "    字段数: " . count($columns) . "\n";
                foreach ($columns as $column) {
                    $pk = $column['pk'] ? ' (主键)' : '';
                    $notnull = $column['notnull'] ? ' NOT NULL' : '';
                    echo "      - {$column['name']}: {$column['type']}$notnull$pk\n";
                }
                
                // 获取记录数
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "    记录数: $count\n\n";
            }
            
            $this->analysis['database'] = [
                'type' => 'SQLite',
                'file' => 'data/boyou_bbs.db',
                'tables' => $tables,
                'table_count' => count($tables)
            ];
            
        } catch (PDOException $e) {
            echo "  ✗ 数据库连接失败: " . $e->getMessage() . "\n\n";
        }
    }
    
    private function analyzeSecurityImplementation() {
        echo "6. 分析安全实现...\n";
        
        // 检查安全相关文件和功能
        $securityFeatures = [
            'CSRF保护' => $this->checkCSRFImplementation(),
            'XSS防护' => $this->checkXSSProtection(),
            '密码哈希' => $this->checkPasswordHashing(),
            '会话安全' => $this->checkSessionSecurity(),
            '文件上传安全' => $this->checkUploadSecurity(),
            '权限控制' => $this->checkPermissionSystem()
        ];
        
        foreach ($securityFeatures as $feature => $implemented) {
            $status = $implemented ? '✓' : '✗';
            $message = $implemented ? '已实现' : '未实现或需要改进';
            echo "  $status $feature: $message\n";
        }
        echo "\n";
        
        $this->analysis['security'] = $securityFeatures;
    }
    
    private function analyzeRoutingSystem() {
        echo "7. 分析路由系统...\n";
        
        // 分析前台路由
        echo "  前台路由 (/route/):\n";
        $frontRoutes = glob('route/*.php');
        foreach ($frontRoutes as $route) {
            $routeName = basename($route, '.php');
            $content = file_get_contents($route);
            $actions = $this->extractActions($content);
            echo "    - $routeName: " . implode(', ', $actions) . "\n";
        }
        echo "\n";
        
        // 分析管理后台路由
        echo "  管理后台路由 (/admin/route/):\n";
        $adminRoutes = glob('admin/route/*.php');
        foreach ($adminRoutes as $route) {
            $routeName = basename($route, '.php');
            $content = file_get_contents($route);
            $actions = $this->extractActions($content);
            echo "    - admin/$routeName: " . implode(', ', $actions) . "\n";
        }
        echo "\n";
    }
    
    private function analyzeTemplateSystem() {
        echo "8. 分析模板系统...\n";
        
        // 分析前台模板
        echo "  前台模板 (/view/htm/):\n";
        $frontTemplates = glob('view/htm/*.htm');
        foreach ($frontTemplates as $template) {
            $templateName = basename($template, '.htm');
            echo "    - $templateName.htm\n";
        }
        echo "\n";
        
        // 分析管理后台模板
        echo "  管理后台模板 (/admin/view/):\n";
        $adminTemplates = glob('admin/view/*.htm');
        if (empty($adminTemplates)) {
            echo "    (无独立模板文件，可能使用动态生成)\n";
        } else {
            foreach ($adminTemplates as $template) {
                $templateName = basename($template, '.htm');
                echo "    - $templateName.htm\n";
            }
        }
        echo "\n";
        
        // 分析静态资源
        echo "  静态资源:\n";
        $cssFiles = glob('view/css/*.css');
        $jsFiles = glob('view/js/*.js');
        $imgFiles = glob('view/img/*');
        
        echo "    CSS文件: " . count($cssFiles) . " 个\n";
        echo "    JS文件: " . count($jsFiles) . " 个\n";
        echo "    图片文件: " . count($imgFiles) . " 个\n";
        echo "\n";
    }
    
    private function countFiles($directory) {
        if (!is_dir($directory)) return 0;
        
        $count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }
        
        return $count;
    }
    
    private function formatBytes($size) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unit = 0;
        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }
        return round($size, 2) . ' ' . $units[$unit];
    }
    
    private function checkCSRFImplementation() {
        // 检查CSRF相关函数
        $files = ['boyouphp/boyouphp.php', 'tmp/model.min.php'];
        foreach ($files as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                if (strpos($content, 'csrf_token') !== false) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private function checkXSSProtection() {
        // 检查XSS防护相关代码
        if (file_exists('boyouphp/xn_html_safe.func.php')) {
            return true;
        }
        return false;
    }
    
    private function checkPasswordHashing() {
        // 检查密码哈希相关函数
        if (file_exists('model/user.func.php')) {
            $content = file_get_contents('model/user.func.php');
            return strpos($content, 'password_hash') !== false;
        }
        return false;
    }
    
    private function checkSessionSecurity() {
        // 检查会话安全设置
        if (file_exists('model/session.func.php')) {
            $content = file_get_contents('model/session.func.php');
            return strpos($content, 'httponly') !== false;
        }
        return false;
    }
    
    private function checkUploadSecurity() {
        // 检查文件上传安全
        if (file_exists('model/attach.func.php')) {
            return true;
        }
        return false;
    }
    
    private function checkPermissionSystem() {
        // 检查权限系统
        if (file_exists('model/user.func.php')) {
            $content = file_get_contents('model/user.func.php');
            return strpos($content, 'user_is_admin') !== false;
        }
        return false;
    }
    
    private function extractActions($content) {
        $actions = [];
        
        // 提取 action 参数处理
        if (preg_match_all('/\$action\s*==\s*[\'"]([^\'"]+)[\'"]/', $content, $matches)) {
            $actions = array_merge($actions, $matches[1]);
        }
        
        // 提取 method 处理
        if (strpos($content, 'GET') !== false) $actions[] = 'GET';
        if (strpos($content, 'POST') !== false) $actions[] = 'POST';
        
        return array_unique($actions);
    }
    
    private function generateStructureReport() {
        echo "\n=== 项目结构分析报告 ===\n\n";
        
        echo "📊 项目统计:\n";
        $totalDirs = count($this->analysis['directories']);
        $existingDirs = count(array_filter($this->analysis['directories'], function($d) { return $d['exists']; }));
        echo "  目录结构完整性: $existingDirs/$totalDirs (" . round(($existingDirs/$totalDirs)*100, 1) . "%)\n";
        
        if (isset($this->analysis['database'])) {
            echo "  数据库表数量: {$this->analysis['database']['table_count']}\n";
        }
        
        echo "  路由数量: " . (count($this->analysis['architecture']['routes']) + count(glob('admin/route/*.php'))) . "\n";
        echo "  模型数量: " . count($this->analysis['architecture']['models']) . "\n";
        
        echo "\n🏗️ 架构特点:\n";
        echo "  - 采用 MVC 架构模式\n";
        echo "  - 基于路由的请求分发\n";
        echo "  - 模块化的功能组织\n";
        echo "  - 前后台分离设计\n";
        echo "  - SQLite 数据库存储\n";
        echo "  - 模板化的视图系统\n";
        
        echo "\n🔒 安全特性:\n";
        foreach ($this->analysis['security'] as $feature => $implemented) {
            $status = $implemented ? '✓' : '✗';
            echo "  $status $feature\n";
        }
        
        echo "\n📁 关键目录说明:\n";
        echo "  /boyouphp/ - 框架核心，提供基础功能\n";
        echo "  /route/ - 前台控制器，处理用户请求\n";
        echo "  /admin/ - 管理后台，独立的管理系统\n";
        echo "  /model/ - 数据模型，封装数据操作\n";
        echo "  /view/ - 前台模板和静态资源\n";
        echo "  /conf/ - 配置文件，系统参数设置\n";
        echo "  /data/ - 数据存储，包括数据库文件\n";
        
        // 保存分析结果
        file_put_contents('project_structure_analysis.json', json_encode($this->analysis, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n📄 详细分析结果已保存到: project_structure_analysis.json\n";
    }
}

// 主程序
$analyzer = new ProjectStructureAnalyzer();
$analyzer->runCompleteAnalysis();

echo "\n=== 项目结构分析完成 ===\n";

?>
