<?php

echo "=== 文件重命名脚本 ===\n\n";

// 重命名xiuno.js为boyou.js
if (file_exists('view/js/xiuno.js')) {
    if (copy('view/js/xiuno.js', 'view/js/boyou.js')) {
        echo "✓ 成功复制 xiuno.js 到 boyou.js\n";
        if (unlink('view/js/xiuno.js')) {
            echo "✓ 成功删除原 xiuno.js 文件\n";
        } else {
            echo "✗ 删除原 xiuno.js 文件失败\n";
        }
    } else {
        echo "✗ 复制 xiuno.js 失败\n";
    }
} else {
    echo "! xiuno.js 文件不存在或已重命名\n";
}

// 检查boyou.js是否存在
if (file_exists('view/js/boyou.js')) {
    echo "✓ boyou.js 文件存在\n";
} else {
    echo "✗ boyou.js 文件不存在\n";
}

echo "\n=== 重命名完成 ===\n";

?>
