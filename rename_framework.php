<?php

echo "=== 重命名框架目录和文件 ===\n\n";

// 创建boyouphp目录
if (!is_dir('boyouphp')) {
    if (mkdir('boyouphp', 0755)) {
        echo "✓ 创建 boyouphp 目录成功\n";
    } else {
        echo "✗ 创建 boyouphp 目录失败\n";
        exit(1);
    }
} else {
    echo "! boyouphp 目录已存在\n";
}

// 获取xiunophp目录中的所有文件
$sourceDir = 'boyouphp';
$targetDir = 'boyouphp';

if (!is_dir($sourceDir)) {
    echo "✗ 源目录 $sourceDir 不存在\n";
    exit(1);
}

$files = scandir($sourceDir);
$copiedFiles = 0;
$renamedFiles = [];

foreach ($files as $file) {
    if ($file === '.' || $file === '..') continue;
    
    $sourcePath = $sourceDir . '/' . $file;
    
    // 重命名规则
    $newFileName = $file;
    if ($file === 'boyouphp.php') {
        $newFileName = 'boyouphp.php';
        $renamedFiles[] = "$file → $newFileName";
    } elseif ($file === 'boyouphp.min.php') {
        $newFileName = 'boyouphp.min.php';
        $renamedFiles[] = "$file → $newFileName";
    }
    
    $targetPath = $targetDir . '/' . $newFileName;
    
    if (is_file($sourcePath)) {
        if (copy($sourcePath, $targetPath)) {
            echo "✓ 复制: $file → $newFileName\n";
            $copiedFiles++;
        } else {
            echo "✗ 复制失败: $file\n";
        }
    }
}

echo "\n=== 复制结果 ===\n";
echo "复制文件数: $copiedFiles\n";

if (!empty($renamedFiles)) {
    echo "重命名文件:\n";
    foreach ($renamedFiles as $rename) {
        echo "  $rename\n";
    }
}

// 验证关键文件
$keyFiles = ['boyouphp.php', 'boyouphp.min.php'];
echo "\n=== 验证关键文件 ===\n";
foreach ($keyFiles as $file) {
    $path = $targetDir . '/' . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✓ $file 存在 (" . number_format($size) . " 字节)\n";
    } else {
        echo "✗ $file 不存在\n";
    }
}

echo "\n=== 重命名完成 ===\n";

?>
