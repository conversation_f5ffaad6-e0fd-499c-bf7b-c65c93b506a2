<?php

echo "重命名 tmp 目录中的文件...\n";

// 重命名xiunophp相关的文件
$oldFile = 'tmp/xiunophp_xn_send_mail.func.php';
$newFile = 'tmp/boyouphp_xn_send_mail.func.php';

if (file_exists($oldFile)) {
    if (copy($oldFile, $newFile)) {
        echo "✓ 成功复制 $oldFile 到 $newFile\n";
        if (unlink($oldFile)) {
            echo "✓ 成功删除原文件 $oldFile\n";
        } else {
            echo "⚠️  删除原文件失败: $oldFile\n";
        }
    } else {
        echo "✗ 复制文件失败\n";
    }
} else {
    echo "! 文件不存在: $oldFile\n";
}

// 检查是否有其他需要重命名的文件
$tmpFiles = scandir('tmp');
$xiunoFiles = array_filter($tmpFiles, function($file) {
    return strpos($file, 'xiuno') !== false;
});

if (!empty($xiunoFiles)) {
    echo "\n发现其他包含 xiuno 的文件:\n";
    foreach ($xiunoFiles as $file) {
        echo "  $file\n";
    }
} else {
    echo "\n✓ tmp 目录中没有其他包含 xiuno 的文件\n";
}

echo "\n重命名完成\n";

?>
