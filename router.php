<?php
/**
 * Boyou BBS 6.1 路由器脚本
 * 用于PHP内置服务器的安全路由处理
 */

$request_uri = $_SERVER['REQUEST_URI'];
$request_path = parse_url($request_uri, PHP_URL_PATH);
$file_path = __DIR__ . $request_path;

// 保护敏感路径
$protected_paths = ['/conf/', '/data/', '/log/', '/tmp/', '/.git/', '/admin/route/', '/model/'];
foreach ($protected_paths as $path) {
    if (strpos($request_path, $path) !== false) {
        http_response_code(403);
        echo 'Access Denied';
        exit;
    }
}

// 保护敏感文件扩展名
$protected_extensions = ['.db', '.sql', '.log', '.conf', '.bak', '.backup', '.inc'];
foreach ($protected_extensions as $ext) {
    if (substr($request_path, -strlen($ext)) === $ext) {
        http_response_code(403);
        echo 'Access Denied';
        exit;
    }
}

// 如果请求的是PHP文件，只允许特定的入口文件
if (substr($request_path, -4) === '.php') {
    $allowed_php_files = ['/index.php', '/admin/index.php'];
    if (!in_array($request_path, $allowed_php_files)) {
        http_response_code(403);
        echo 'Access Denied';
        exit;
    }
}

// 如果文件存在且不是被保护的，让PHP内置服务器处理
if (file_exists($file_path) && is_file($file_path)) {
    return false; // 让PHP内置服务器处理
}

// 否则路由到index.php
include __DIR__ . '/index.php';
?>
