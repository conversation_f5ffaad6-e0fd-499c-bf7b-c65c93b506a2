<?php

echo "=== 项目安全漏洞分析 ===\n\n";

$vulnerabilities = [];
$fixedIssues = [];
$recommendations = [];

// 1. 检查SQL注入漏洞
echo "1. 检查SQL注入漏洞...\n";

$sqlInjectionPatterns = [
    '/\$_GET\[.*?\].*?mysql_query/i',
    '/\$_POST\[.*?\].*?mysql_query/i',
    '/\$_REQUEST\[.*?\].*?mysql_query/i',
    '/mysql_query.*?\$_[GET|POST|REQUEST]/i',
    '/query.*?\$_[GET|POST|REQUEST].*?[^escape]/i'
];

$directories = ['.', 'admin', 'plugin', 'model'];
$sqlVulnFiles = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() !== 'php') continue;
        
        $filePath = $file->getPathname();
        if (strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        
        foreach ($sqlInjectionPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $sqlVulnFiles[] = $filePath;
                break;
            }
        }
    }
}

if (!empty($sqlVulnFiles)) {
    echo "⚠️  发现潜在SQL注入风险文件:\n";
    foreach ($sqlVulnFiles as $file) {
        echo "  - $file\n";
    }
    $vulnerabilities[] = "SQL注入风险: " . count($sqlVulnFiles) . " 个文件";
} else {
    echo "✓ 未发现明显的SQL注入漏洞\n";
}

// 2. 检查XSS漏洞
echo "\n2. 检查XSS漏洞...\n";

$xssPatterns = [
    '/echo\s+\$_[GET|POST|REQUEST]/i',
    '/print\s+\$_[GET|POST|REQUEST]/i',
    '/\?\>\s*\$_[GET|POST|REQUEST]/i',
    '/echo.*?\$_[GET|POST|REQUEST].*?[^htmlspecialchars]/i'
];

$xssVulnFiles = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if (!in_array($file->getExtension(), ['php', 'htm'])) continue;
        
        $filePath = $file->getPathname();
        if (strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $xssVulnFiles[] = $filePath;
                break;
            }
        }
    }
}

if (!empty($xssVulnFiles)) {
    echo "⚠️  发现潜在XSS风险文件:\n";
    foreach ($xssVulnFiles as $file) {
        echo "  - $file\n";
    }
    $vulnerabilities[] = "XSS风险: " . count($xssVulnFiles) . " 个文件";
} else {
    echo "✓ 未发现明显的XSS漏洞\n";
}

// 3. 检查文件上传漏洞
echo "\n3. 检查文件上传漏洞...\n";

$uploadPatterns = [
    '/move_uploaded_file.*?\$_FILES/i',
    '/\$_FILES.*?move_uploaded_file/i',
    '/file_put_contents.*?\$_[GET|POST]/i'
];

$uploadVulnFiles = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() !== 'php') continue;
        
        $filePath = $file->getPathname();
        if (strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        
        foreach ($uploadPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $uploadVulnFiles[] = $filePath;
                break;
            }
        }
    }
}

if (!empty($uploadVulnFiles)) {
    echo "⚠️  发现文件上传相关文件:\n";
    foreach ($uploadVulnFiles as $file) {
        echo "  - $file\n";
    }
    $vulnerabilities[] = "文件上传需要检查: " . count($uploadVulnFiles) . " 个文件";
} else {
    echo "✓ 未发现文件上传相关代码\n";
}

// 4. 检查CSRF保护
echo "\n4. 检查CSRF保护...\n";

$csrfProtected = false;
$csrfFiles = [];

// 检查是否有CSRF令牌生成和验证
if (function_exists('csrf_token_generate') && function_exists('csrf_token_verify')) {
    echo "✓ CSRF保护函数已定义\n";
    $csrfProtected = true;
} else {
    echo "⚠️  CSRF保护函数未定义\n";
    $vulnerabilities[] = "缺少CSRF保护机制";
}

// 检查表单是否使用CSRF保护
$formFiles = [];
foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if (!in_array($file->getExtension(), ['php', 'htm'])) continue;
        
        $filePath = $file->getPathname();
        $content = file_get_contents($filePath);
        
        if (preg_match('/<form.*?method=["\']post["\']/i', $content)) {
            $formFiles[] = $filePath;
            
            if (strpos($content, 'csrf_token') !== false) {
                $csrfFiles[] = $filePath;
            }
        }
    }
}

echo "发现 " . count($formFiles) . " 个POST表单文件\n";
echo "其中 " . count($csrfFiles) . " 个使用了CSRF保护\n";

if (count($formFiles) > count($csrfFiles)) {
    $unprotectedForms = count($formFiles) - count($csrfFiles);
    echo "⚠️  有 $unprotectedForms 个表单可能缺少CSRF保护\n";
    $vulnerabilities[] = "部分表单缺少CSRF保护";
}

// 5. 检查敏感信息泄露
echo "\n5. 检查敏感信息泄露...\n";

$sensitivePatterns = [
    '/password\s*=\s*["\'][^"\']+["\']/i',
    '/mysql.*?password.*?=.*?["\'][^"\']+["\']/i',
    '/api_key\s*=\s*["\'][^"\']+["\']/i',
    '/secret\s*=\s*["\'][^"\']+["\']/i'
];

$sensitiveFiles = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() !== 'php') continue;
        
        $filePath = $file->getPathname();
        $content = file_get_contents($filePath);
        
        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $sensitiveFiles[] = $filePath;
                break;
            }
        }
    }
}

if (!empty($sensitiveFiles)) {
    echo "⚠️  发现可能包含敏感信息的文件:\n";
    foreach ($sensitiveFiles as $file) {
        echo "  - $file\n";
    }
    $vulnerabilities[] = "敏感信息泄露风险: " . count($sensitiveFiles) . " 个文件";
} else {
    echo "✓ 未发现明显的敏感信息泄露\n";
}

// 6. 检查目录遍历漏洞
echo "\n6. 检查目录遍历漏洞...\n";

$pathTraversalPatterns = [
    '/include.*?\$_[GET|POST|REQUEST]/i',
    '/require.*?\$_[GET|POST|REQUEST]/i',
    '/file_get_contents.*?\$_[GET|POST|REQUEST]/i',
    '/fopen.*?\$_[GET|POST|REQUEST]/i'
];

$pathTraversalFiles = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() !== 'php') continue;
        
        $filePath = $file->getPathname();
        if (strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        
        foreach ($pathTraversalPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $pathTraversalFiles[] = $filePath;
                break;
            }
        }
    }
}

if (!empty($pathTraversalFiles)) {
    echo "⚠️  发现潜在目录遍历风险文件:\n";
    foreach ($pathTraversalFiles as $file) {
        echo "  - $file\n";
    }
    $vulnerabilities[] = "目录遍历风险: " . count($pathTraversalFiles) . " 个文件";
} else {
    echo "✓ 未发现明显的目录遍历漏洞\n";
}

// 7. 生成安全报告
echo "\n=== 安全分析报告 ===\n";

if (empty($vulnerabilities)) {
    echo "🎉 未发现严重安全漏洞！\n";
    echo "\n✅ 安全检查通过项目:\n";
    echo "  - SQL注入防护\n";
    echo "  - XSS防护\n";
    echo "  - 文件上传安全\n";
    echo "  - 敏感信息保护\n";
    echo "  - 目录遍历防护\n";
} else {
    echo "⚠️  发现以下安全问题:\n";
    foreach ($vulnerabilities as $vuln) {
        echo "  - $vuln\n";
    }
}

// 8. 安全建议
echo "\n=== 安全建议 ===\n";

$recommendations = [
    "启用HTTPS加密传输",
    "定期更新PHP版本",
    "配置安全的文件权限",
    "启用Web应用防火墙(WAF)",
    "定期备份数据",
    "监控异常访问日志",
    "使用强密码策略",
    "限制管理员访问IP"
];

foreach ($recommendations as $rec) {
    echo "  💡 $rec\n";
}

echo "\n=== 安全分析完成 ===\n";

// 保存报告
$report = [
    'scan_time' => date('Y-m-d H:i:s'),
    'vulnerabilities' => $vulnerabilities,
    'sql_injection_files' => $sqlVulnFiles,
    'xss_files' => $xssVulnFiles,
    'upload_files' => $uploadVulnFiles,
    'sensitive_files' => $sensitiveFiles,
    'path_traversal_files' => $pathTraversalFiles,
    'csrf_protected' => $csrfProtected,
    'form_files' => count($formFiles),
    'csrf_files' => count($csrfFiles),
    'recommendations' => $recommendations
];

file_put_contents('security_analysis_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n详细安全报告已保存到: security_analysis_report.json\n";

?>
