<?php

echo "=== Boyou BBS 6.1 安全漏洞扫描 ===\n\n";

class SecurityScanner {
    private $vulnerabilities = [];
    private $scannedFiles = 0;
    private $excludeDirs = ['boyouphp', 'tmp', 'upload', 'data', '.git', 'node_modules'];
    
    public function scan() {
        echo "开始安全漏洞扫描...\n\n";
        
        $this->scanSQLInjection();
        $this->scanXSS();
        $this->scanCSRF();
        $this->scanFileUpload();
        $this->scanPathTraversal();
        $this->scanCodeInjection();
        $this->scanSessionSecurity();
        $this->scanPasswordSecurity();
        $this->scanInputValidation();
        $this->scanErrorHandling();
        
        $this->generateReport();
    }
    
    private function scanSQLInjection() {
        echo "1. 扫描SQL注入漏洞...\n";
        
        $patterns = [
            '/\$_(?:GET|POST|REQUEST)\[.*?\].*?(?:mysql_query|mysqli_query|query)\s*\(/i',
            '/\$_(?:GET|POST|REQUEST)\[.*?\].*?(?:SELECT|INSERT|UPDATE|DELETE)/i',
            '/(?:SELECT|INSERT|UPDATE|DELETE).*?\$_(?:GET|POST|REQUEST)/i',
            '/query\s*\(\s*["\'].*?\$_(?:GET|POST|REQUEST)/i',
            '/["\'].*?\$_(?:GET|POST|REQUEST).*?["\'].*?(?:mysql_query|query)/i'
        ];
        
        $this->scanPatterns($patterns, 'SQL注入', 'HIGH');
    }
    
    private function scanXSS() {
        echo "2. 扫描XSS漏洞...\n";
        
        $patterns = [
            '/echo\s+\$_(?:GET|POST|REQUEST)\[.*?\]/i',
            '/print\s+\$_(?:GET|POST|REQUEST)\[.*?\]/i',
            '/\?>\s*\$_(?:GET|POST|REQUEST)/i',
            '/value\s*=\s*["\']?\$_(?:GET|POST|REQUEST)/i',
            '/innerHTML.*?\$_(?:GET|POST|REQUEST)/i'
        ];
        
        $this->scanPatterns($patterns, 'XSS跨站脚本', 'HIGH');
    }
    
    private function scanCSRF() {
        echo "3. 扫描CSRF漏洞...\n";
        
        $this->scanFiles(function($file, $content) {
            // 检查POST表单是否有CSRF保护
            if (preg_match('/method\s*=\s*["\']post["\']/', $content, $matches)) {
                if (!preg_match('/csrf_token|_token|authenticity_token/', $content)) {
                    $this->addVulnerability($file, 'CSRF', 'POST表单缺少CSRF令牌保护', 'MEDIUM', $matches[0]);
                }
            }
            
            // 检查重要操作是否验证CSRF
            if (preg_match('/(?:delete|update|insert|create).*?(?:\$_POST|\$_REQUEST)/i', $content, $matches)) {
                if (!preg_match('/csrf.*?verify|token.*?check/', $content)) {
                    $this->addVulnerability($file, 'CSRF', '重要操作缺少CSRF验证', 'HIGH', $matches[0]);
                }
            }
        });
    }
    
    private function scanFileUpload() {
        echo "4. 扫描文件上传漏洞...\n";
        
        $patterns = [
            '/move_uploaded_file\s*\(\s*\$_FILES.*?\$_(?:GET|POST|REQUEST)/i',
            '/\$_FILES\[.*?\]\[.*?\].*?(?:exec|system|shell_exec)/i',
            '/\$_FILES.*?\.php/i'
        ];
        
        $this->scanPatterns($patterns, '文件上传', 'HIGH');
        
        // 检查文件类型验证
        $this->scanFiles(function($file, $content) {
            if (preg_match('/\$_FILES/', $content)) {
                if (!preg_match('/(?:pathinfo|mime_content_type|finfo_file)/', $content)) {
                    $this->addVulnerability($file, '文件上传', '文件上传缺少类型验证', 'HIGH', '$_FILES');
                }
            }
        });
    }
    
    private function scanPathTraversal() {
        echo "5. 扫描路径遍历漏洞...\n";
        
        $patterns = [
            '/(?:include|require).*?\$_(?:GET|POST|REQUEST)/i',
            '/file_get_contents.*?\$_(?:GET|POST|REQUEST)/i',
            '/fopen.*?\$_(?:GET|POST|REQUEST)/i',
            '/readfile.*?\$_(?:GET|POST|REQUEST)/i'
        ];
        
        $this->scanPatterns($patterns, '路径遍历', 'HIGH');
    }
    
    private function scanCodeInjection() {
        echo "6. 扫描代码注入漏洞...\n";
        
        $patterns = [
            '/eval\s*\(\s*\$_(?:GET|POST|REQUEST)/i',
            '/exec\s*\(\s*\$_(?:GET|POST|REQUEST)/i',
            '/system\s*\(\s*\$_(?:GET|POST|REQUEST)/i',
            '/shell_exec\s*\(\s*\$_(?:GET|POST|REQUEST)/i',
            '/preg_replace.*?\/e.*?\$_(?:GET|POST|REQUEST)/i'
        ];
        
        $this->scanPatterns($patterns, '代码注入', 'CRITICAL');
    }
    
    private function scanSessionSecurity() {
        echo "7. 扫描会话安全...\n";
        
        $this->scanFiles(function($file, $content) {
            // 检查session配置
            if (preg_match('/session_start/', $content)) {
                if (!preg_match('/session_regenerate_id/', $content)) {
                    $this->addVulnerability($file, '会话安全', '缺少会话ID重新生成', 'MEDIUM', 'session_start');
                }
            }
            
            // 检查敏感信息存储在session中
            if (preg_match('/\$_SESSION.*?(?:password|pwd|pass)/', $content, $matches)) {
                $this->addVulnerability($file, '会话安全', '敏感信息存储在会话中', 'MEDIUM', $matches[0]);
            }
        });
    }
    
    private function scanPasswordSecurity() {
        echo "8. 扫描密码安全...\n";
        
        $this->scanFiles(function($file, $content) {
            // 检查明文密码
            if (preg_match('/password\s*=\s*["\'][^"\']*["\']/', $content, $matches)) {
                if (!preg_match('/\*+/', $matches[0])) {
                    $this->addVulnerability($file, '密码安全', '可能包含明文密码', 'HIGH', $matches[0]);
                }
            }
            
            // 检查弱密码哈希
            if (preg_match('/md5\s*\(\s*\$.*?password/', $content, $matches)) {
                $this->addVulnerability($file, '密码安全', '使用弱密码哈希算法MD5', 'MEDIUM', $matches[0]);
            }
            
            if (preg_match('/sha1\s*\(\s*\$.*?password/', $content, $matches)) {
                $this->addVulnerability($file, '密码安全', '使用弱密码哈希算法SHA1', 'MEDIUM', $matches[0]);
            }
        });
    }
    
    private function scanInputValidation() {
        echo "9. 扫描输入验证...\n";
        
        $this->scanFiles(function($file, $content) {
            // 检查直接使用用户输入
            $userInputs = ['$_GET', '$_POST', '$_REQUEST', '$_COOKIE'];
            foreach ($userInputs as $input) {
                if (preg_match('/' . preg_quote($input) . '\[.*?\](?!\s*=)/', $content, $matches)) {
                    // 检查是否有验证或过滤
                    $context = substr($content, max(0, strpos($content, $matches[0]) - 100), 200);
                    if (!preg_match('/(?:filter_|htmlspecialchars|strip_tags|intval|floatval|is_numeric|preg_match)/', $context)) {
                        $this->addVulnerability($file, '输入验证', '用户输入缺少验证或过滤', 'MEDIUM', $matches[0]);
                    }
                }
            }
        });
    }
    
    private function scanErrorHandling() {
        echo "10. 扫描错误处理...\n";
        
        $this->scanFiles(function($file, $content) {
            // 检查错误信息泄露
            if (preg_match('/mysql_error\(\)|mysqli_error\(\)/', $content, $matches)) {
                $this->addVulnerability($file, '信息泄露', '数据库错误信息可能泄露', 'LOW', $matches[0]);
            }
            
            // 检查调试信息
            if (preg_match('/var_dump\(|print_r\(|var_export\(/', $content, $matches)) {
                $this->addVulnerability($file, '信息泄露', '调试信息可能泄露', 'LOW', $matches[0]);
            }
        });
    }
    
    private function scanPatterns($patterns, $type, $severity) {
        foreach ($patterns as $pattern) {
            $this->scanFiles(function($file, $content) use ($pattern, $type, $severity) {
                if (preg_match($pattern, $content, $matches)) {
                    $this->addVulnerability($file, $type, '发现潜在漏洞', $severity, $matches[0]);
                }
            });
        }
    }
    
    private function scanFiles($callback) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator('.', RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if (!$file->isFile()) continue;
            
            $path = $file->getPathname();
            $extension = strtolower($file->getExtension());
            
            // 跳过非PHP文件和排除目录
            if ($extension !== 'php') continue;
            
            $skip = false;
            foreach ($this->excludeDirs as $excludeDir) {
                if (strpos($path, $excludeDir . '/') !== false) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;
            
            $content = file_get_contents($path);
            if ($content === false) continue;
            
            $this->scannedFiles++;
            $callback($path, $content);
        }
    }
    
    private function addVulnerability($file, $type, $description, $severity, $code) {
        $this->vulnerabilities[] = [
            'file' => $file,
            'type' => $type,
            'description' => $description,
            'severity' => $severity,
            'code' => trim($code),
            'line' => $this->getLineNumber($file, $code)
        ];
    }
    
    private function getLineNumber($file, $code) {
        $content = file_get_contents($file);
        $lines = explode("\n", $content);
        foreach ($lines as $num => $line) {
            if (strpos($line, $code) !== false) {
                return $num + 1;
            }
        }
        return 0;
    }
    
    private function generateReport() {
        echo "\n=== 安全扫描报告 ===\n";
        echo "扫描文件数: {$this->scannedFiles}\n";
        echo "发现漏洞数: " . count($this->vulnerabilities) . "\n\n";
        
        if (empty($this->vulnerabilities)) {
            echo "🎉 未发现明显的安全漏洞！\n";
            return;
        }
        
        // 按严重程度分组
        $grouped = [];
        foreach ($this->vulnerabilities as $vuln) {
            $grouped[$vuln['severity']][] = $vuln;
        }
        
        $severityOrder = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'];
        $severityColors = [
            'CRITICAL' => '🔴',
            'HIGH' => '🟠', 
            'MEDIUM' => '🟡',
            'LOW' => '🔵'
        ];
        
        foreach ($severityOrder as $severity) {
            if (!isset($grouped[$severity])) continue;
            
            echo "{$severityColors[$severity]} {$severity} 级别漏洞 (" . count($grouped[$severity]) . "个):\n";
            
            foreach ($grouped[$severity] as $vuln) {
                echo "  文件: {$vuln['file']}:{$vuln['line']}\n";
                echo "  类型: {$vuln['type']}\n";
                echo "  描述: {$vuln['description']}\n";
                echo "  代码: {$vuln['code']}\n";
                echo "  ---\n";
            }
            echo "\n";
        }
        
        // 保存详细报告
        $reportData = [
            'scan_time' => date('Y-m-d H:i:s'),
            'scanned_files' => $this->scannedFiles,
            'total_vulnerabilities' => count($this->vulnerabilities),
            'vulnerabilities' => $this->vulnerabilities
        ];
        
        file_put_contents('security_scan_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "详细报告已保存到: security_scan_report.json\n";
    }
}

// 执行扫描
$scanner = new SecurityScanner();
$scanner->scan();

echo "\n=== 扫描完成 ===\n";

?>
