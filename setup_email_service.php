<?php

echo "=== Boyou BBS 6.1 邮件服务配置工具 ===\n\n";

class EmailServiceSetup {
    private $smtpConfig;
    private $configFile = 'conf/smtp.conf.php';
    
    public function __construct() {
        $this->smtpConfig = include $this->configFile;
    }
    
    public function runSetup() {
        echo "开始邮件服务配置...\n\n";
        
        $this->displayCurrentConfig();
        $this->showConfigurationGuide();
        $this->testEmailFunction();
        $this->generateConfigurationInstructions();
    }
    
    private function displayCurrentConfig() {
        echo "1. 当前邮件配置状态:\n";
        
        $status = $this->smtpConfig['smtp_on'] ? '✓ 已启用' : '✗ 未启用';
        echo "  SMTP状态: $status\n";
        
        if ($this->smtpConfig['smtp_on']) {
            echo "  SMTP服务器: {$this->smtpConfig['smtp_server']}\n";
            echo "  SMTP端口: {$this->smtpConfig['smtp_port']}\n";
            echo "  加密方式: {$this->smtpConfig['smtp_ssl']}\n";
            echo "  发件人: {$this->smtpConfig['mail_from']}\n";
        } else {
            echo "  ⚠️  邮件服务未配置，以下功能将无法使用:\n";
            echo "    - 用户注册邮箱验证\n";
            echo "    - 密码重置邮件\n";
            echo "    - 系统通知邮件\n";
        }
        echo "\n";
    }
    
    private function showConfigurationGuide() {
        echo "2. 邮件服务配置指南:\n\n";
        
        echo "📧 常用邮件服务商配置:\n\n";
        
        $providers = [
            'Gmail' => [
                'server' => 'smtp.gmail.com',
                'port' => 587,
                'ssl' => 'tls',
                'steps' => [
                    '1. 开启两步验证',
                    '2. 生成应用专用密码',
                    '3. 使用应用专用密码作为SMTP密码'
                ]
            ],
            'QQ邮箱' => [
                'server' => 'smtp.qq.com',
                'port' => 587,
                'ssl' => 'tls',
                'steps' => [
                    '1. 登录QQ邮箱',
                    '2. 设置 -> 账户 -> 开启SMTP服务',
                    '3. 获取授权码作为SMTP密码'
                ]
            ],
            '163邮箱' => [
                'server' => 'smtp.163.com',
                'port' => 587,
                'ssl' => 'tls',
                'steps' => [
                    '1. 登录163邮箱',
                    '2. 设置 -> POP3/SMTP/IMAP',
                    '3. 开启SMTP服务并设置客户端授权密码'
                ]
            ]
        ];
        
        foreach ($providers as $name => $config) {
            echo "🔹 $name:\n";
            echo "   服务器: {$config['server']}\n";
            echo "   端口: {$config['port']}\n";
            echo "   加密: {$config['ssl']}\n";
            echo "   配置步骤:\n";
            foreach ($config['steps'] as $step) {
                echo "     $step\n";
            }
            echo "\n";
        }
    }
    
    private function testEmailFunction() {
        echo "3. 测试邮件功能:\n";
        
        // 检查PHP邮件扩展
        if (function_exists('mail')) {
            echo "  ✓ PHP mail() 函数可用\n";
        } else {
            echo "  ✗ PHP mail() 函数不可用\n";
        }
        
        // 检查SMTP相关函数
        if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            echo "  ✓ PHPMailer 库已安装\n";
        } else {
            echo "  ⚠️  PHPMailer 库未安装，建议安装以获得更好的邮件支持\n";
            echo "     安装命令: composer require phpmailer/phpmailer\n";
        }
        
        // 检查系统邮件模型
        if (file_exists('model/smtp.func.php')) {
            echo "  ✓ 邮件模型文件存在\n";
            
            // 检查邮件函数
            include_once 'model/smtp.func.php';
            if (function_exists('smtp_send')) {
                echo "  ✓ smtp_send 函数可用\n";
            } else {
                echo "  ✗ smtp_send 函数不可用\n";
            }
        } else {
            echo "  ✗ 邮件模型文件不存在\n";
        }
        
        echo "\n";
    }
    
    private function generateConfigurationInstructions() {
        echo "4. 配置说明:\n\n";
        
        echo "📝 手动配置步骤:\n";
        echo "1. 编辑文件: conf/smtp.conf.php\n";
        echo "2. 修改以下配置项:\n";
        echo "   - smtp_on: 设为 true\n";
        echo "   - smtp_server: 您的SMTP服务器地址\n";
        echo "   - smtp_port: SMTP端口号\n";
        echo "   - smtp_ssl: 加密方式 (tls/ssl)\n";
        echo "   - smtp_username: 您的邮箱地址\n";
        echo "   - smtp_password: 您的邮箱密码或授权码\n";
        echo "   - mail_from: 发件人邮箱地址\n";
        echo "   - mail_from_name: 发件人名称\n\n";
        
        echo "🔧 配置示例 (Gmail):\n";
        $exampleConfig = [
            'smtp_on' => true,
            'smtp_server' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_ssl' => 'tls',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your-app-password',
            'mail_from' => '<EMAIL>',
            'mail_from_name' => 'Boyou BBS'
        ];
        
        foreach ($exampleConfig as $key => $value) {
            $valueStr = is_bool($value) ? ($value ? 'true' : 'false') : "'$value'";
            echo "   '$key' => $valueStr,\n";
        }
        echo "\n";
        
        echo "⚠️  安全提醒:\n";
        echo "1. 不要在代码中硬编码邮箱密码\n";
        echo "2. 使用应用专用密码而不是账户密码\n";
        echo "3. 定期更换邮箱授权码\n";
        echo "4. 限制SMTP访问IP（如果支持）\n\n";
        
        echo "🧪 测试邮件发送:\n";
        echo "配置完成后，可以通过以下方式测试:\n";
        echo "1. 访问管理后台 -> 系统设置 -> 邮件设置\n";
        echo "2. 发送测试邮件\n";
        echo "3. 检查邮件是否正常发送和接收\n\n";
    }
    
    public function createTestEmailScript() {
        echo "5. 创建邮件测试脚本:\n";
        
        $testScript = '<?php
// 邮件发送测试脚本
define("APP_PATH", __DIR__ . "/");
include APP_PATH . "conf/conf.php";
include APP_PATH . "boyouphp/boyouphp.php";
include APP_PATH . "model/smtp.func.php";

$to = "<EMAIL>";  // 修改为您的测试邮箱
$subject = "Boyou BBS 邮件测试";
$message = "这是一封来自 Boyou BBS 的测试邮件。如果您收到此邮件，说明邮件服务配置成功！";

if (smtp_send($to, $subject, $message)) {
    echo "✓ 邮件发送成功！\n";
} else {
    echo "✗ 邮件发送失败！\n";
}
?>';
        
        file_put_contents('test_email.php', $testScript);
        echo "  ✓ 邮件测试脚本已创建: test_email.php\n";
        echo "  使用方法: php test_email.php\n";
        echo "  (请先修改脚本中的测试邮箱地址)\n\n";
    }
    
    public function checkEmailDependencies() {
        echo "6. 检查邮件依赖:\n";
        
        $dependencies = [
            'openssl' => '用于SSL/TLS连接',
            'sockets' => '用于网络连接',
            'mbstring' => '用于多字节字符串处理'
        ];
        
        foreach ($dependencies as $ext => $description) {
            if (extension_loaded($ext)) {
                echo "  ✓ $ext 扩展已加载 ($description)\n";
            } else {
                echo "  ✗ $ext 扩展未加载 ($description)\n";
            }
        }
        
        echo "\n";
    }
    
    public function generateQuickSetupScript() {
        echo "7. 生成快速配置脚本:\n";
        
        $quickSetup = '#!/bin/bash
# Boyou BBS 邮件服务快速配置脚本

echo "=== Boyou BBS 邮件服务快速配置 ==="
echo

read -p "请输入SMTP服务器地址: " smtp_server
read -p "请输入SMTP端口 (默认587): " smtp_port
smtp_port=${smtp_port:-587}
read -p "请输入加密方式 (tls/ssl，默认tls): " smtp_ssl
smtp_ssl=${smtp_ssl:-tls}
read -p "请输入邮箱用户名: " smtp_username
read -s -p "请输入邮箱密码/授权码: " smtp_password
echo
read -p "请输入发件人邮箱: " mail_from
read -p "请输入发件人名称 (默认Boyou BBS): " mail_from_name
mail_from_name=${mail_from_name:-"Boyou BBS"}

# 生成配置文件
cat > conf/smtp.conf.php << EOF
<?php
return [
    "smtp_on" => true,
    "smtp_server" => "$smtp_server",
    "smtp_port" => $smtp_port,
    "smtp_ssl" => "$smtp_ssl",
    "smtp_username" => "$smtp_username",
    "smtp_password" => "$smtp_password",
    "mail_from" => "$mail_from",
    "mail_from_name" => "$mail_from_name",
    "mail_charset" => "UTF-8",
    "mail_html" => true
];
?>
EOF

echo "✓ 邮件配置已保存到 conf/smtp.conf.php"
echo "✓ 配置完成！请测试邮件发送功能"
';
        
        file_put_contents('quick_email_setup.sh', $quickSetup);
        chmod('quick_email_setup.sh', 0755);
        
        echo "  ✓ 快速配置脚本已创建: quick_email_setup.sh\n";
        echo "  使用方法: ./quick_email_setup.sh\n\n";
    }
}

// 主程序
$setup = new EmailServiceSetup();
$setup->runSetup();
$setup->checkEmailDependencies();
$setup->createTestEmailScript();
$setup->generateQuickSetupScript();

echo "=== 邮件服务配置完成 ===\n";
echo "\n💡 下一步:\n";
echo "1. 根据您的邮件服务商配置 conf/smtp.conf.php\n";
echo "2. 运行 php test_email.php 测试邮件发送\n";
echo "3. 在管理后台启用邮件功能\n";
echo "4. 测试用户注册和密码重置功能\n";

?>
