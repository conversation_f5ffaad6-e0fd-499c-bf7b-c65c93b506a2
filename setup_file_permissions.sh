#!/bin/bash

# Boyou BBS 6.1 文件权限设置脚本
# 此脚本用于设置适当的文件和目录权限，提高系统安全性

echo "=== Boyou BBS 6.1 文件权限设置 ==="
echo

# 检查是否为root用户或有sudo权限
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  检测到root用户，请确保这是您想要的操作"
    echo "建议使用普通用户运行此脚本"
    echo
fi

# 获取当前目录
PROJECT_DIR=$(pwd)
echo "项目目录: $PROJECT_DIR"
echo

# 设置基本权限
echo "1. 设置基本文件和目录权限..."

# 设置目录权限为755 (rwxr-xr-x)
echo "  设置目录权限为755..."
find . -type d -exec chmod 755 {} \; 2>/dev/null

# 设置普通文件权限为644 (rw-r--r--)
echo "  设置普通文件权限为644..."
find . -type f -exec chmod 644 {} \; 2>/dev/null

# 设置PHP文件权限为644
echo "  设置PHP文件权限为644..."
find . -name "*.php" -exec chmod 644 {} \; 2>/dev/null

echo "  ✓ 基本权限设置完成"
echo

# 设置可写目录权限
echo "2. 设置可写目录权限..."

writable_dirs=(
    "tmp"
    "upload" 
    "log"
    "data"
)

for dir in "${writable_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  设置 $dir 目录为可写 (755)..."
        chmod 755 "$dir"
        # 如果需要Web服务器写入，可能需要775或777
        # chmod 775 "$dir"
        echo "  ✓ $dir 权限设置完成"
    else
        echo "  ⚠️  目录 $dir 不存在，跳过"
    fi
done

echo

# 保护敏感文件
echo "3. 保护敏感文件..."

sensitive_files=(
    "conf/conf.php"
    "conf/security.php"
    "conf/smtp.conf.php"
    "data/boyou_bbs.db"
    ".htaccess"
    "nginx.conf"
)

for file in "${sensitive_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  保护敏感文件: $file (权限设为600)..."
        chmod 600 "$file"
        echo "  ✓ $file 保护完成"
    else
        echo "  ⚠️  文件 $file 不存在，跳过"
    fi
done

echo

# 保护敏感目录
echo "4. 保护敏感目录..."

sensitive_dirs=(
    "conf"
    "data"
    "log"
    ".git"
)

for dir in "${sensitive_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  保护敏感目录: $dir (权限设为750)..."
        chmod 750 "$dir"
        echo "  ✓ $dir 保护完成"
    else
        echo "  ⚠️  目录 $dir 不存在，跳过"
    fi
done

echo

# 设置执行权限
echo "5. 设置执行权限..."

executable_files=(
    "setup_file_permissions.sh"
)

for file in "${executable_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  设置执行权限: $file..."
        chmod 755 "$file"
        echo "  ✓ $file 执行权限设置完成"
    fi
done

echo

# 移除危险权限
echo "6. 移除危险权限..."

echo "  移除所有文件的执行权限（除了脚本文件）..."
find . -name "*.php" -exec chmod -x {} \; 2>/dev/null
find . -name "*.html" -exec chmod -x {} \; 2>/dev/null
find . -name "*.htm" -exec chmod -x {} \; 2>/dev/null
find . -name "*.css" -exec chmod -x {} \; 2>/dev/null
find . -name "*.js" -exec chmod -x {} \; 2>/dev/null

echo "  ✓ 危险权限移除完成"
echo

# 特殊处理上传目录
echo "7. 特殊处理上传目录..."

if [ -d "upload" ]; then
    echo "  设置上传目录安全权限..."
    chmod 755 upload
    
    # 移除上传目录中所有文件的执行权限
    find upload -type f -exec chmod 644 {} \; 2>/dev/null
    
    # 创建.htaccess文件保护上传目录
    cat > upload/.htaccess << 'EOF'
# 禁止执行PHP文件
<FilesMatch "\.php$">
    Require all denied
</FilesMatch>

# 只允许特定文件类型
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$">
    Require all granted
</FilesMatch>

# 其他文件类型拒绝访问
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|pdf|doc|docx|txt|zip)$).*$">
    Require all denied
</FilesMatch>
EOF
    
    echo "  ✓ 上传目录安全设置完成"
else
    echo "  ⚠️  上传目录不存在，跳过"
fi

echo

# 创建权限检查脚本
echo "8. 创建权限检查脚本..."

cat > check_permissions.sh << 'EOF'
#!/bin/bash

echo "=== Boyou BBS 6.1 权限检查 ==="
echo

echo "敏感文件权限检查:"
files=("conf/conf.php" "data/boyou_bbs.db" ".htaccess")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
        echo "  $file: $perms"
    fi
done

echo
echo "可写目录权限检查:"
dirs=("tmp" "upload" "log" "data")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(stat -c "%a" "$dir" 2>/dev/null || stat -f "%A" "$dir" 2>/dev/null)
        echo "  $dir/: $perms"
    fi
done

echo
echo "检查完成"
EOF

chmod 755 check_permissions.sh
echo "  ✓ 权限检查脚本创建完成"

echo

# 显示权限设置摘要
echo "=== 权限设置摘要 ==="
echo "✓ 基本文件权限: 644 (rw-r--r--)"
echo "✓ 基本目录权限: 755 (rwxr-xr-x)"
echo "✓ 敏感文件权限: 600 (rw-------)"
echo "✓ 敏感目录权限: 750 (rwxr-x---)"
echo "✓ 可写目录权限: 755 (rwxr-xr-x)"
echo "✓ 上传目录已保护"
echo "✓ 执行权限已移除"

echo
echo "=== 安全建议 ==="
echo "1. 定期检查文件权限: ./check_permissions.sh"
echo "2. 监控敏感文件的访问"
echo "3. 定期备份重要数据"
echo "4. 考虑使用SELinux或AppArmor进一步加强安全"

echo
echo "=== 权限设置完成 ==="

# 运行权限检查
echo
echo "当前权限状态:"
./check_permissions.sh
