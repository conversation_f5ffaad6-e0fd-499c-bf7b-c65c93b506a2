<?php

echo "=== 简单检查剩余的 xiuno 引用 ===\n\n";

// 检查关键文件中的xiuno引用
$keyFiles = [
    'index.php',
    'conf/conf.php',
    'README.md',
    'view/htm/footer.inc.htm',
    'admin/view/htm/footer.inc.htm',
    'install/view/htm/footer.inc.htm',
    'lang/zh-cn/bbs_install.php'
];

$foundReferences = [];

foreach ($keyFiles as $file) {
    if (!file_exists($file)) {
        echo "文件不存在: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    foreach ($lines as $lineNum => $line) {
        // 查找xiuno相关引用（不区分大小写）
        if (preg_match('/xiuno/i', $line)) {
            $foundReferences[] = [
                'file' => $file,
                'line' => $lineNum + 1,
                'content' => trim($line)
            ];
        }
    }
}

if (empty($foundReferences)) {
    echo "✓ 在关键文件中没有发现 xiuno 引用\n";
} else {
    echo "⚠️  发现以下 xiuno 引用:\n\n";
    foreach ($foundReferences as $ref) {
        echo "文件: {$ref['file']}\n";
        echo "行号: {$ref['line']}\n";
        echo "内容: {$ref['content']}\n";
        echo "---\n";
    }
}

// 检查是否还有xiunophp目录
if (is_dir('boyouphp')) {
    echo "\n⚠️  xiunophp 目录仍然存在\n";
    $files = scandir('boyouphp');
    echo "目录内容: " . implode(', ', array_filter($files, function($f) { return $f !== '.' && $f !== '..'; })) . "\n";
} else {
    echo "\n✓ xiunophp 目录已不存在\n";
}

// 检查boyouphp目录
if (is_dir('boyouphp')) {
    echo "✓ boyouphp 目录存在\n";
    $files = scandir('boyouphp');
    $phpFiles = array_filter($files, function($f) { return pathinfo($f, PATHINFO_EXTENSION) === 'php'; });
    echo "PHP文件数量: " . count($phpFiles) . "\n";
    echo "主要文件: " . implode(', ', array_slice($phpFiles, 0, 5)) . "\n";
} else {
    echo "✗ boyouphp 目录不存在\n";
}

// 检查view/js目录中的文件
echo "\n=== 检查 JavaScript 文件 ===\n";
if (is_dir('view/js')) {
    $jsFiles = scandir('view/js');
    $jsFiles = array_filter($jsFiles, function($f) { return pathinfo($f, PATHINFO_EXTENSION) === 'js'; });
    
    if (in_array('boyou.js', $jsFiles)) {
        echo "⚠️  boyou.js 仍然存在\n";
    } else {
        echo "✓ boyou.js 已不存在\n";
    }
    
    if (in_array('boyou.js', $jsFiles)) {
        echo "✓ boyou.js 存在\n";
    } else {
        echo "⚠️  boyou.js 不存在\n";
    }
    
    echo "JS文件: " . implode(', ', $jsFiles) . "\n";
}

// 检查配置文件中的版本信息
echo "\n=== 检查版本信息 ===\n";
if (file_exists('conf/conf.php')) {
    $conf = include 'conf/conf.php';
    echo "配置文件版本: " . ($conf['version'] ?? '未设置') . "\n";
    echo "站点名称: " . ($conf['sitename'] ?? '未设置') . "\n";
    echo "数据库名: " . ($conf['db']['master']['name'] ?? '未设置') . "\n";
}

echo "\n=== 检查完成 ===\n";

?>
