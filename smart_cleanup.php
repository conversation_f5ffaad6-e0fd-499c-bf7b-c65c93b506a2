<?php

echo "=== 智能清理：删除 xiunophp 目录 ===\n\n";

// 1. 检查boyouphp目录完整性
echo "1. 检查boyouphp目录完整性...\n";

if (!is_dir('boyouphp')) {
    echo "✗ boyouphp 目录不存在，无法进行清理\n";
    exit(1);
}

$boyouFiles = scandir('boyouphp');
$boyouPhpFiles = array_filter($boyouFiles, function($f) { 
    return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
});

echo "boyouphp 目录包含 " . count($boyouPhpFiles) . " 个PHP文件\n";

// 检查关键文件
$keyFiles = ['boyouphp.php', 'boyouphp.min.php', 'db.func.php', 'misc.func.php'];
$missingKeyFiles = [];

foreach ($keyFiles as $file) {
    if (!file_exists("boyouphp/$file")) {
        $missingKeyFiles[] = $file;
    }
}

if (!empty($missingKeyFiles)) {
    echo "⚠️  关键文件缺失: " . implode(', ', $missingKeyFiles) . "\n";
    exit(1);
}

echo "✓ boyouphp 目录完整性检查通过\n";

// 2. 检查核心文件中的引用（忽略临时脚本）
echo "\n2. 检查核心文件中的xiunophp引用...\n";

$coreFiles = [
    'index.php',
    'install/index.php',
    'admin/index.php',
    'view/htm/footer.inc.htm',
    'admin/view/htm/footer.inc.htm',
    'tmp/model.min.php',
    'tmp/model.inc.php'
];

$coreReferences = [];
foreach ($coreFiles as $file) {
    if (!file_exists($file)) continue;
    
    $content = file_get_contents($file);
    if (strpos($content, 'xiunophp') !== false) {
        $coreReferences[] = $file;
    }
}

if (!empty($coreReferences)) {
    echo "⚠️  核心文件仍引用 xiunophp:\n";
    foreach ($coreReferences as $file) {
        echo "  $file\n";
    }
    exit(1);
}

echo "✓ 核心文件没有xiunophp引用\n";

// 3. 测试框架功能
echo "\n3. 测试框架功能...\n";

try {
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', './');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', './boyouphp/');
    
    ob_start();
    include_once 'boyouphp/boyouphp.php';
    $output = ob_get_clean();
    
    if (defined('BOYOUPHP_VERSION')) {
        echo "✓ BoyouPHP 框架正常工作，版本: " . BOYOUPHP_VERSION . "\n";
    } else {
        echo "⚠️  框架版本常量未定义\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "✗ 框架测试失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. 创建备份
echo "\n4. 创建备份...\n";

if (!is_dir('xiunophp')) {
    echo "! xiunophp 目录不存在，无需处理\n";
    echo "✓ 清理工作已完成\n";
    exit(0);
}

$backupDir = 'xiunophp_backup_' . date('Y-m-d_H-i-s');
echo "创建备份目录: $backupDir\n";

$command = "cp -r xiunophp $backupDir";
exec($command, $output, $returnCode);

if ($returnCode === 0 && is_dir($backupDir)) {
    echo "✓ 备份创建成功: $backupDir\n";
} else {
    echo "⚠️  备份创建失败\n";
    exit(1);
}

// 5. 删除xiunophp目录
echo "\n5. 删除 xiunophp 目录...\n";

function deleteDirectory($dir) {
    if (!is_dir($dir)) return false;
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

if (deleteDirectory('xiunophp')) {
    echo "✓ xiunophp 目录删除成功\n";
} else {
    echo "✗ xiunophp 目录删除失败\n";
    exit(1);
}

// 6. 最终验证
echo "\n6. 最终验证...\n";

if (is_dir('xiunophp')) {
    echo "✗ xiunophp 目录仍然存在\n";
} else {
    echo "✓ xiunophp 目录已成功删除\n";
}

if (is_dir('boyouphp')) {
    echo "✓ boyouphp 目录正常存在\n";
} else {
    echo "✗ boyouphp 目录不存在！\n";
}

echo "\n=== 清理工作完成 ===\n";

echo "\n🎉 xiunophp 目录清理成功！\n";
echo "\n📋 清理总结:\n";
echo "  ✅ xiunophp 目录已删除\n";
echo "  ✅ boyouphp 目录正常工作\n";
echo "  ✅ 备份已创建: $backupDir\n";
echo "  ✅ 框架功能正常\n";

echo "\n💡 后续建议:\n";
echo "  - 测试网站所有功能\n";
echo "  - 确认无误后可删除备份: $backupDir\n";
echo "  - 可删除临时脚本文件\n";

?>
