<?php

// 测试管理员登录
define('DEBUG', 1);

// 包含主文件
include './index.php';

echo "<h1>管理员登录测试</h1>";

// 模拟POST请求到管理员登录
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    echo "<h2>处理登录请求</h2>";
    echo "<p>密码: " . ($password ? '已提供' : '未提供') . "</p>";
    echo "<p>CSRF令牌: " . ($csrf_token ? '已提供' : '未提供') . "</p>";
    
    // 检查用户状态
    $user = G('user');
    if (!$user) {
        echo "<p style='color: red;'>错误：用户未登录</p>";
        echo "<p><a href='/?user-login.htm'>请先登录前台</a></p>";
        exit;
    }
    
    echo "<p>当前用户: {$user['username']} (UID: {$user['uid']})</p>";
    
    // 检查是否为管理员
    $gid = G('gid');
    if (!user_is_admin($gid)) {
        echo "<p style='color: red;'>错误：当前用户不是管理员</p>";
        exit;
    }
    
    echo "<p style='color: green;'>用户是管理员，继续验证密码...</p>";
    
    // 验证密码
    if (empty($password)) {
        echo "<p style='color: red;'>错误：密码不能为空</p>";
        exit;
    }
    
    // 验证CSRF令牌
    if (!csrf_token_verify($csrf_token)) {
        echo "<p style='color: red;'>错误：CSRF令牌验证失败</p>";
        exit;
    }
    
    // 验证管理员密码
    if (!user_password_verify($password, $user['password'])) {
        echo "<p style='color: red;'>错误：密码不正确</p>";
        exit;
    }
    
    echo "<p style='color: green;'>密码验证成功！</p>";
    
    // 设置管理员令牌
    admin_token_set();
    
    echo "<p style='color: green;'>管理员令牌已设置，登录成功！</p>";
    echo "<p><a href='/admin/'>进入管理后台</a></p>";
    
} else {
    // 显示登录表单
    $user = G('user');
    
    if (!$user) {
        echo "<p style='color: red;'>请先登录前台用户</p>";
        echo "<p><a href='/?user-login.htm'>前台登录</a></p>";
        exit;
    }
    
    echo "<p>当前用户: {$user['username']} (UID: {$user['uid']})</p>";
    
    $gid = G('gid');
    if (!user_is_admin($gid)) {
        echo "<p style='color: red;'>当前用户不是管理员</p>";
        echo "<p>用户组ID: $gid</p>";
        exit;
    }
    
    echo "<p style='color: green;'>用户是管理员，可以进行管理员验证</p>";
    
    // 生成CSRF令牌
    $csrf_token = csrf_token_generate();
    
    echo '<form method="post" action="">
        <h3>管理员密码验证</h3>
        <p>
            <label>管理员密码:</label><br>
            <input type="password" name="password" required>
        </p>
        <input type="hidden" name="csrf_token" value="' . htmlspecialchars($csrf_token) . '">
        <p>
            <button type="submit">验证</button>
        </p>
    </form>';
    
    echo "<p><strong>提示:</strong> 请使用管理员密码: <code>Admin123!@#</code></p>";
}

?>
