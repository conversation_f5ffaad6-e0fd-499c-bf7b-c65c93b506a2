<?php

echo "=== Boyou BBS 6.1 完整功能测试 ===\n\n";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

function test_result($name, $result, $message = '') {
    global $test_results, $total_tests, $passed_tests;
    $total_tests++;
    if ($result) {
        $passed_tests++;
        echo "✓ $name\n";
        if ($message) echo "  $message\n";
    } else {
        echo "✗ $name\n";
        if ($message) echo "  $message\n";
    }
    $test_results[$name] = ['result' => $result, 'message' => $message];
}

echo "开始完整功能测试...\n\n";

// 1. 安装状态测试
echo "=== 1. 安装状态测试 ===\n";
test_result('安装锁文件存在', file_exists('./data/install.lock'), '安装时间: ' . (file_exists('./data/install.lock') ? file_get_contents('./data/install.lock') : '未安装'));
test_result('配置文件存在', file_exists('./conf/conf.php'));
test_result('数据库文件存在', file_exists('./data/boyou_bbs.db'));

// 2. 配置文件测试
echo "\n=== 2. 配置文件测试 ===\n";
$conf = null;
if (file_exists('./conf/conf.php')) {
    $conf = include './conf/conf.php';
    test_result('配置文件加载', is_array($conf));
    test_result('站点名称配置', isset($conf['sitename']), $conf['sitename'] ?? '未设置');
    test_result('站点版本配置', isset($conf['version']), $conf['version'] ?? '未设置');
    test_result('数据库配置', isset($conf['db']));
} else {
    test_result('配置文件加载', false, '配置文件不存在');
}

// 3. 数据库连接测试
echo "\n=== 3. 数据库连接测试 ===\n";
$db_connected = false;
$user_count = 0;
$forum_count = 0;
$admin_exists = false;

try {
    $pdo = new PDO("sqlite:./data/boyou_bbs.db");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db_connected = true;
    test_result('数据库连接', true);
    
    // 测试表是否存在
    $tables = ['bbs_user', 'bbs_group', 'bbs_forum', 'bbs_thread', 'bbs_post'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            test_result("表 $table 存在", true, "记录数: $count");
            
            if ($table === 'bbs_user') $user_count = $count;
            if ($table === 'bbs_forum') $forum_count = $count;
        } catch (Exception $e) {
            test_result("表 $table 存在", false, $e->getMessage());
        }
    }
    
    // 检查管理员账户
    try {
        $stmt = $pdo->prepare("SELECT username FROM bbs_user WHERE group_id = 1 LIMIT 1");
        $stmt->execute();
        $admin_user = $stmt->fetchColumn();
        if ($admin_user) {
            $admin_exists = true;
            test_result('管理员账户存在', true, "用户名: $admin_user");
        } else {
            test_result('管理员账户存在', false);
        }
    } catch (Exception $e) {
        test_result('管理员账户检查', false, $e->getMessage());
    }
    
} catch (Exception $e) {
    test_result('数据库连接', false, $e->getMessage());
}

// 4. 框架文件测试
echo "\n=== 4. 框架文件测试 ===\n";
$framework_files = [
    'boyouphp/boyouphp.php' => 'BoyouPHP主文件',
    'boyouphp/boyouphp.min.php' => 'BoyouPHP编译版',
    'boyouphp/db.func.php' => '数据库函数',
    'boyouphp/cache.func.php' => '缓存函数',
    'boyouphp/misc.func.php' => '杂项函数',
    'boyouphp/array.func.php' => '数组函数',
    'boyouphp/image.func.php' => '图片函数',
    'boyouphp/xn_encrypt.func.php' => '加密函数'
];

foreach ($framework_files as $file => $desc) {
    test_result($desc, file_exists($file), $file);
}

// 5. 安全功能测试
echo "\n=== 5. 安全功能测试 ===\n";
$security_files = [
    'boyouphp/security_middleware.php' => '安全中间件',
    'boyouphp/template_security.php' => '模板安全函数',
    'conf/security.php' => '安全配置文件'
];

foreach ($security_files as $file => $desc) {
    test_result($desc, file_exists($file), $file);
}

// 6. 目录权限测试
echo "\n=== 6. 目录权限测试 ===\n";
$directories = [
    'upload' => '上传目录',
    'upload/attach' => '附件目录',
    'upload/avatar' => '头像目录',
    'upload/forum' => '版块图片目录',
    'tmp' => '临时目录',
    'log' => '日志目录',
    'data' => '数据目录'
];

foreach ($directories as $dir => $desc) {
    $exists = is_dir($dir);
    $writable = $exists && is_writable($dir);
    test_result("$desc 可写", $writable, $exists ? ($writable ? '可写' : '不可写') : '不存在');
}

// 7. PHP扩展测试
echo "\n=== 7. PHP扩展测试 ===\n";
$extensions = [
    'pdo' => 'PDO数据库扩展',
    'pdo_sqlite' => 'SQLite扩展',
    'json' => 'JSON扩展',
    'mbstring' => '多字节字符串扩展',
    'gd' => 'GD图像扩展',
    'openssl' => 'OpenSSL加密扩展',
    'curl' => 'cURL网络扩展'
];

foreach ($extensions as $ext => $desc) {
    test_result($desc, extension_loaded($ext));
}

// 8. 功能模块测试
echo "\n=== 8. 功能模块测试 ===\n";

// 测试框架加载
try {
    if (file_exists('./boyouphp/boyouphp.php')) {
        // 简单的语法检查
        $syntax_check = shell_exec("php -l ./boyouphp/boyouphp.php 2>&1");
        $syntax_ok = strpos($syntax_check, 'No syntax errors') !== false;
        test_result('BoyouPHP语法检查', $syntax_ok, $syntax_ok ? '语法正确' : '语法错误');
    }
} catch (Exception $e) {
    test_result('BoyouPHP语法检查', false, $e->getMessage());
}

// 9. 网络访问测试
echo "\n=== 9. 网络访问测试 ===\n";
$urls = [
    'http://localhost:8000/test_installation.php' => '安装测试页面',
    'http://localhost:8000/health_check.php' => '健康检查页面',
    'http://localhost:8000/js_test.html' => 'JavaScript测试页面'
];

foreach ($urls as $url => $desc) {
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'method' => 'GET'
            ]
        ]);
        $response = @file_get_contents($url, false, $context);
        $accessible = $response !== false;
        test_result($desc, $accessible, $accessible ? '可访问' : '无法访问');
    } catch (Exception $e) {
        test_result($desc, false, $e->getMessage());
    }
}

// 10. 性能测试
echo "\n=== 10. 性能测试 ===\n";
$start_time = microtime(true);
$start_memory = memory_get_usage();

// 模拟一些操作
for ($i = 0; $i < 1000; $i++) {
    $data = json_encode(['test' => $i]);
    $decoded = json_decode($data, true);
}

$end_time = microtime(true);
$end_memory = memory_get_usage();

$execution_time = ($end_time - $start_time) * 1000; // 转换为毫秒
$memory_used = ($end_memory - $start_memory) / 1024; // 转换为KB

test_result('性能测试', $execution_time < 100, sprintf('执行时间: %.2fms, 内存使用: %.2fKB', $execution_time, $memory_used));

// 生成测试报告
echo "\n=== 测试报告 ===\n";
$success_rate = ($passed_tests / $total_tests) * 100;

echo "总测试数: $total_tests\n";
echo "通过测试: $passed_tests\n";
echo "失败测试: " . ($total_tests - $passed_tests) . "\n";
echo "成功率: " . round($success_rate, 1) . "%\n\n";

if ($success_rate >= 90) {
    echo "🎉 测试结果: 优秀 - 系统运行完全正常\n";
    echo "✅ 所有核心功能都正常工作\n";
    echo "✅ 安装完成，可以正常使用\n";
} elseif ($success_rate >= 80) {
    echo "✅ 测试结果: 良好 - 系统基本正常\n";
    echo "⚠️  有少量问题，但不影响基本使用\n";
} elseif ($success_rate >= 70) {
    echo "⚠️  测试结果: 一般 - 系统有一些问题\n";
    echo "🔧 建议修复问题后再使用\n";
} else {
    echo "❌ 测试结果: 不合格 - 系统有严重问题\n";
    echo "🚨 需要重新安装或修复\n";
}

echo "\n=== 系统信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "操作系统: " . PHP_OS . "\n";
echo "内存限制: " . ini_get('memory_limit') . "\n";
echo "最大执行时间: " . ini_get('max_execution_time') . "秒\n";
echo "当前内存使用: " . round(memory_get_usage() / 1024 / 1024, 2) . "MB\n";
echo "峰值内存使用: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . "MB\n";

if ($success_rate >= 90) {
    echo "\n🚀 下一步建议:\n";
    echo "1. 访问论坛首页: http://localhost:8000/\n";
    echo "2. 登录管理后台: http://localhost:8000/admin/\n";
    echo "3. 管理员账户: admin / admin123\n";
    echo "4. 创建版块和设置权限\n";
    echo "5. 测试用户注册和发帖功能\n";
    echo "6. 配置邮件和其他高级功能\n";
}

echo "\n=== 功能测试完成 ===\n";

?>
