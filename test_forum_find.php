<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 测试 forum_find ===\n";

try {
    // 设置基本环境
    define('DEBUG', 1);
    define('APP_PATH', __DIR__.'/');
    define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    
    // 加载配置
    $conf = include APP_PATH.'conf/conf.php';
    $_SERVER['conf'] = $conf;
    
    // 加载框架
    include BOYOUPHP_PATH.'boyouphp.php';
    
    // 加载模型
    include _include(APP_PATH.'model.inc.php');
    
    echo "1. 环境设置完成\n";
    
    // 直接测试数据库查询
    echo "2. 测试直接数据库查询...\n";
    $result = db_find('forum', array(), array('rank'=>-1), 1, 1000, 'fid');
    echo "   查询结果类型: " . gettype($result) . "\n";
    if (is_array($result)) {
        echo "   查询结果数量: " . count($result) . "\n";
        echo "   第一个结果: " . print_r(reset($result), true) . "\n";
    } else {
        echo "   查询结果: " . var_export($result, true) . "\n";
    }
    
    echo "3. 测试 forum__find 函数...\n";
    $result2 = forum__find(array(), array('rank'=>-1), 1, 1000);
    echo "   结果类型: " . gettype($result2) . "\n";
    if (is_array($result2)) {
        echo "   结果数量: " . count($result2) . "\n";
    } else {
        echo "   结果: " . var_export($result2, true) . "\n";
    }
    
    echo "4. 测试 forum_find 函数...\n";
    $result3 = forum_find(array(), array('rank'=>-1), 1, 1000);
    echo "   结果类型: " . gettype($result3) . "\n";
    if (is_array($result3)) {
        echo "   结果数量: " . count($result3) . "\n";
    } else {
        echo "   结果: " . var_export($result3, true) . "\n";
    }
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
