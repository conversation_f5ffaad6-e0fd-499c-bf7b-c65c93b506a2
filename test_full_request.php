<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "=== 完整请求测试 ===\n";

try {
    // 模拟HTTP请求环境
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/';
    $_SERVER['HTTP_HOST'] = 'localhost:8000';
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Test)';
    $_GET = array();
    $_POST = array();
    $_REQUEST = array();
    
    echo "1. 环境设置完成\n";
    
    // 开始输出缓冲
    ob_start();
    
    echo "2. 开始包含 index.php...\n";
    
    // 包含主入口文件
    include __DIR__ . '/index.php';
    
    echo "3. index.php 执行完成\n";
    
    // 获取输出内容
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "4. 输出内容长度: " . strlen($output) . " 字节\n";
    
    if (strlen($output) > 0) {
        echo "5. 输出内容前100字符:\n";
        echo substr($output, 0, 100) . "\n";
        echo "...\n";
    } else {
        echo "5. 没有输出内容\n";
    }
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
