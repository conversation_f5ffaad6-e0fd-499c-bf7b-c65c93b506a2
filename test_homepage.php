<?php

// 启用错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 测试主页面 ===\n";

try {
    // 设置基本常量
    define('DEBUG', 1);
    define('APP_PATH', __DIR__.'/');
    define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    
    // 加载配置
    $conf = include APP_PATH.'conf/conf.php';
    $_SERVER['conf'] = $conf;
    
    echo "1. 配置加载成功\n";
    
    // 加载框架
    include BOYOUPHP_PATH.'boyouphp.php';
    echo "2. 框架加载成功\n";
    
    // 加载模型
    include _include(APP_PATH.'model.inc.php');
    echo "3. 模型加载成功\n";
    
    // 检查数据库连接
    if ($_SERVER['db']) {
        echo "4. 数据库连接成功\n";
    } else {
        echo "4. 数据库连接失败\n";
    }
    
    // 测试路由加载
    echo "5. 测试路由加载...\n";
    
    // 模拟请求参数
    $_REQUEST['mod'] = '';
    $_REQUEST['action'] = '';
    
    // 加载路由分发器
    include APP_PATH.'index.inc.php';
    echo "6. 路由分发器加载成功\n";
    
} catch (Error $e) {
    echo "致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
