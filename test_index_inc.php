<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "=== 测试 index.inc.php ===\n";

try {
    // 模拟HTTP请求环境
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/';
    $_SERVER['HTTP_HOST'] = 'localhost:8000';
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Test)';
    $_GET = array();
    $_POST = array();
    $_REQUEST = array();
    
    // 设置常量
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', __DIR__.'/');
    if (!defined('ADMIN_PATH')) define('ADMIN_PATH', APP_PATH.'admin/');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    
    // 加载配置
    $conf = include APP_PATH.'conf/conf.php';
    $_SERVER['conf'] = $conf;
    
    // 加载框架
    include BOYOUPHP_PATH.'boyouphp.php';
    
    // 加载模型
    include _include(APP_PATH.'model.inc.php');
    
    echo "1. 基础环境设置完成\n";
    
    // 逐行测试 index.inc.php 的内容
    echo "2. 开始会话...\n";
    $sid = sess_start();
    echo "   会话ID: $sid\n";
    
    echo "3. 设置安全头部...\n";
    // 跳过安全头部设置，因为我们在命令行环境
    
    echo "4. 加载语言...\n";
    $_SERVER['lang'] = $lang = include _include(APP_PATH."lang/$conf[lang]/bbs.php");
    echo "   语言加载成功\n";
    
    echo "5. 加载用户组...\n";
    $grouplist = group_list_cache();
    echo "   用户组数量: " . count($grouplist) . "\n";
    
    echo "6. 处理用户认证...\n";
    $uid = intval(_SESSION('uid'));
    empty($uid) AND $uid = user_token_get() AND $_SESSION['uid'] = $uid;
    $user = user_read($uid);
    echo "   用户ID: $uid\n";
    
    echo "7. 设置用户组...\n";
    $gid = empty($user) ? 3 : intval($user['gid']);
    $group = isset($grouplist[$gid]) ? $grouplist[$gid] : (isset($grouplist[3]) ? $grouplist[3] : reset($grouplist));
    echo "   用户组ID: $gid\n";
    
    echo "8. 加载版块...\n";
    $fid = 0;
    $forumlist = forum_list_cache();
    echo "   版块列表类型: " . gettype($forumlist) . "\n";
    if (is_array($forumlist)) {
        echo "   版块数量: " . count($forumlist) . "\n";
    } else {
        echo "   版块列表值: " . var_export($forumlist, true) . "\n";
        return; // 停止执行
    }
    
    echo "9. 过滤版块权限...\n";
    $forumlist_show = forum_list_access_filter($forumlist, $gid);
    echo "   可见版块数量: " . count($forumlist_show) . "\n";
    
    echo "10. 设置头部信息...\n";
    $header = array(
        'title'=>$conf['sitename'],
        'mobile_title'=>$conf['sitename'],
        'mobile_link'=>'./',
        'keywords'=>'',
        'description'=>strip_tags($conf['sitebrief']),
        'navs'=>array(),
    );
    echo "    头部信息设置完成\n";
    
    echo "11. 初始化运行时数据...\n";
    $runtime = runtime_init();
    echo "    运行时数据初始化完成\n";
    
    echo "12. 检查运行级别...\n";
    check_runlevel();
    echo "    运行级别检查完成\n";
    
    echo "13. 设置路由...\n";
    $route = param(0, 'index');
    echo "    路由: $route\n";
    
    echo "14. 准备路由分发...\n";
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
