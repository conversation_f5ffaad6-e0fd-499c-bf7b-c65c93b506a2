<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "=== 逐步测试 index.php ===\n";

try {
    // 模拟HTTP请求环境
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/';
    $_SERVER['HTTP_HOST'] = 'localhost:8000';
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Test)';
    $_GET = array();
    $_POST = array();
    $_REQUEST = array();
    
    echo "1. 环境设置完成\n";
    
    // 检查基本路径保护
    echo "2. 检查路径保护...\n";
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    $protected_paths = ['/conf/', '/data/', '/log/', '/tmp/', '/.git/', '/admin/route/', '/model/'];
    foreach ($protected_paths as $path) {
        if (strpos($request_uri, $path) !== false) {
            echo "   路径被保护，退出\n";
            exit('Access Denied');
        }
    }
    echo "   路径检查通过\n";
    
    // 检查文件扩展名保护
    echo "3. 检查文件扩展名保护...\n";
    $protected_extensions = ['.db', '.sql', '.log', '.conf', '.bak', '.backup'];
    foreach ($protected_extensions as $ext) {
        if (substr($request_uri, -strlen($ext)) === $ext) {
            echo "   文件扩展名被保护，退出\n";
            exit('Access Denied');
        }
    }
    echo "   文件扩展名检查通过\n";
    
    // 设置常量
    echo "4. 设置常量...\n";
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', __DIR__.'/');
    if (!defined('ADMIN_PATH')) define('ADMIN_PATH', APP_PATH.'admin/');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    echo "   常量设置完成\n";
    
    // 加载配置
    echo "5. 加载配置...\n";
    $conf = (@include APP_PATH.'conf/conf.php') OR exit('<script>window.location="install/"</script>');
    $_SERVER['conf'] = $conf;
    echo "   配置加载成功\n";
    
    // 兼容性设置
    echo "6. 兼容性设置...\n";
    !isset($conf['user_create_on']) AND $conf['user_create_on'] = 1;
    !isset($conf['logo_mobile_url']) AND $conf['logo_mobile_url'] = 'view/img/logo.png';
    !isset($conf['logo_pc_url']) AND $conf['logo_pc_url'] = 'view/img/logo.png';
    !isset($conf['logo_water_url']) AND $conf['logo_water_url'] = 'view/img/water-small.png';
    $conf['version'] = '4.0.4';
    echo "   兼容性设置完成\n";
    
    // 加载框架
    echo "7. 加载框架...\n";
    if (file_exists(BOYOUPHP_PATH.'boyouphp.php')) {
        include BOYOUPHP_PATH.'boyouphp.php';
        echo "   框架加载成功\n";
    } else {
        echo "   框架文件不存在\n";
        exit;
    }
    
    // 检查数据库连接
    echo "8. 检查数据库连接...\n";
    if (isset($_SERVER['db'])) {
        echo "   数据库对象已创建\n";
    } else {
        echo "   数据库对象未创建\n";
    }
    
    echo "9. 准备加载模型...\n";

    // 加载模型
    echo "10. 加载模型...\n";
    if (file_exists(APP_PATH.'model.inc.php')) {
        include _include(APP_PATH.'model.inc.php');
        echo "    模型加载成功\n";
    } else {
        echo "    模型文件不存在\n";
        exit;
    }

    // 加载安全配置
    echo "11. 加载安全配置...\n";
    if (file_exists(APP_PATH.'conf/security.php')) {
        $security_conf = include APP_PATH.'conf/security.php';
        echo "    安全配置加载成功\n";
    } else {
        echo "    安全配置文件不存在\n";
    }

    // 加载路由分发器
    echo "12. 准备加载路由分发器...\n";
    if (file_exists(APP_PATH.'index.inc.php')) {
        echo "    开始包含 index.inc.php...\n";
        include APP_PATH.'index.inc.php';
        echo "    路由分发器加载完成\n";
    } else {
        echo "    路由分发器文件不存在\n";
        exit;
    }
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
