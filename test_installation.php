<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 - 安装测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 40px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #28a745;
        }
        .test-card.warning {
            border-left-color: #ffc107;
        }
        .test-card.error {
            border-left-color: #dc3545;
        }
        .test-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
        }
        .badge.success {
            background: #d4edda;
            color: #155724;
        }
        .badge.warning {
            background: #fff3cd;
            color: #856404;
        }
        .badge.error {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .summary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .summary h2 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Boyou BBS 6.1</h1>
            <p>安装测试和功能验证</p>
        </div>
        
        <div class="content">
            <?php
            // 测试安装状态
            $install_lock_exists = file_exists('./data/install.lock');
            $config_exists = file_exists('./conf/conf.php');
            $database_exists = file_exists('./data/boyou_bbs.db');
            
            $install_time = $install_lock_exists ? file_get_contents('./data/install.lock') : '未安装';
            
            // 测试配置文件
            $conf = null;
            if ($config_exists) {
                $conf = include './conf/conf.php';
            }
            
            // 测试数据库连接
            $db_status = false;
            $user_count = 0;
            $forum_count = 0;
            if ($database_exists) {
                try {
                    $pdo = new PDO("sqlite:./data/boyou_bbs.db");
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    $db_status = true;
                    
                    $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_user");
                    $user_count = $stmt->fetchColumn();
                    
                    $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_forum");
                    $forum_count = $stmt->fetchColumn();
                } catch (Exception $e) {
                    $db_status = false;
                }
            }
            
            // 测试框架文件
            $framework_exists = file_exists('./boyouphp/boyouphp.php');
            $framework_min_exists = file_exists('./boyouphp/boyouphp.min.php');
            
            // 计算总体状态
            $total_tests = 7;
            $passed_tests = 0;
            if ($install_lock_exists) $passed_tests++;
            if ($config_exists) $passed_tests++;
            if ($database_exists) $passed_tests++;
            if ($db_status) $passed_tests++;
            if ($framework_exists) $passed_tests++;
            if ($framework_min_exists) $passed_tests++;
            if ($user_count > 0) $passed_tests++;
            
            $success_rate = ($passed_tests / $total_tests) * 100;
            ?>
            
            <div class="summary">
                <h2>📊 安装状态总览</h2>
                <p><strong>测试通过率: <?php echo round($success_rate, 1); ?>%</strong></p>
                <p>通过测试: <?php echo $passed_tests; ?>/<?php echo $total_tests; ?></p>
                <?php if ($success_rate >= 90): ?>
                    <p style="color: #28a745; font-weight: bold;">🎉 安装成功！系统运行正常</p>
                <?php elseif ($success_rate >= 70): ?>
                    <p style="color: #ffc107; font-weight: bold;">⚠️ 安装基本完成，有部分问题</p>
                <?php else: ?>
                    <p style="color: #dc3545; font-weight: bold;">❌ 安装未完成或有严重问题</p>
                <?php endif; ?>
            </div>
            
            <div class="test-grid">
                <div class="test-card">
                    <h3>📋 安装状态</h3>
                    <div class="test-item">
                        <span>安装锁文件</span>
                        <span class="badge <?php echo $install_lock_exists ? 'success' : 'error'; ?>">
                            <?php echo $install_lock_exists ? '✓ 存在' : '✗ 缺失'; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>安装时间</span>
                        <span class="badge <?php echo $install_lock_exists ? 'success' : 'error'; ?>">
                            <?php echo $install_time; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>配置文件</span>
                        <span class="badge <?php echo $config_exists ? 'success' : 'error'; ?>">
                            <?php echo $config_exists ? '✓ 存在' : '✗ 缺失'; ?>
                        </span>
                    </div>
                </div>
                
                <div class="test-card">
                    <h3>🗄️ 数据库状态</h3>
                    <div class="test-item">
                        <span>数据库文件</span>
                        <span class="badge <?php echo $database_exists ? 'success' : 'error'; ?>">
                            <?php echo $database_exists ? '✓ 存在' : '✗ 缺失'; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>数据库连接</span>
                        <span class="badge <?php echo $db_status ? 'success' : 'error'; ?>">
                            <?php echo $db_status ? '✓ 正常' : '✗ 失败'; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>用户数量</span>
                        <span class="badge <?php echo $user_count > 0 ? 'success' : 'warning'; ?>">
                            <?php echo $user_count; ?> 个
                        </span>
                    </div>
                    <div class="test-item">
                        <span>版块数量</span>
                        <span class="badge <?php echo $forum_count > 0 ? 'success' : 'warning'; ?>">
                            <?php echo $forum_count; ?> 个
                        </span>
                    </div>
                </div>
                
                <div class="test-card">
                    <h3>🔧 框架状态</h3>
                    <div class="test-item">
                        <span>BoyouPHP 框架</span>
                        <span class="badge <?php echo $framework_exists ? 'success' : 'error'; ?>">
                            <?php echo $framework_exists ? '✓ 存在' : '✗ 缺失'; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>编译版本</span>
                        <span class="badge <?php echo $framework_min_exists ? 'success' : 'error'; ?>">
                            <?php echo $framework_min_exists ? '✓ 存在' : '✗ 缺失'; ?>
                        </span>
                    </div>
                    <div class="test-item">
                        <span>PHP版本</span>
                        <span class="badge success"><?php echo PHP_VERSION; ?></span>
                    </div>
                </div>
                
                <div class="test-card">
                    <h3>⚙️ 系统配置</h3>
                    <?php if ($conf): ?>
                        <div class="test-item">
                            <span>站点名称</span>
                            <span class="badge success"><?php echo $conf['sitename'] ?? '未设置'; ?></span>
                        </div>
                        <div class="test-item">
                            <span>站点版本</span>
                            <span class="badge success"><?php echo $conf['version'] ?? '6.1.0'; ?></span>
                        </div>
                        <div class="test-item">
                            <span>数据库类型</span>
                            <span class="badge success">SQLite</span>
                        </div>
                    <?php else: ?>
                        <div class="test-item">
                            <span>配置加载</span>
                            <span class="badge error">✗ 失败</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($success_rate >= 90): ?>
                <div style="text-align: center; margin-top: 40px;">
                    <h3>🚀 安装成功！可以开始使用</h3>
                    <p>管理员账户: <strong>admin</strong> / <strong>admin123</strong></p>
                    <a href="/" class="btn btn-success">🏠 访问论坛首页</a>
                    <a href="admin/" class="btn">⚙️ 管理后台</a>
                    <a href="health_check.php" class="btn">🏥 健康检查</a>
                </div>
            <?php else: ?>
                <div style="text-align: center; margin-top: 40px;">
                    <h3>⚠️ 安装未完成</h3>
                    <p>请检查上述问题并重新安装</p>
                    <a href="install/" class="btn">🔧 重新安装</a>
                    <a href="auto_install.php" class="btn">🤖 自动安装</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
