<?php

echo "=== JavaScript功能测试 ===\n\n";

// 1. 检查JavaScript文件
echo "1. 检查JavaScript文件...\n";

$jsFiles = [
    'view/js/boyou.js' => 'Boyou框架JS',
    'view/js/xiuno.js' => 'Xiuno框架JS（应该删除）',
    'view/js/jquery.min.js' => 'jQuery库',
    'view/js/bootstrap.min.js' => 'Bootstrap库'
];

$jsStatus = [];
foreach ($jsFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✓ $file ($description) - " . number_format($size) . " 字节\n";
        $jsStatus[$file] = true;
    } else {
        echo "✗ $file ($description) - 不存在\n";
        $jsStatus[$file] = false;
    }
}

// 2. 检查boyou.js内容
echo "\n2. 检查boyou.js内容...\n";

if (file_exists('view/js/boyou.js')) {
    $content = file_get_contents('view/js/boyou.js');
    
    // 检查关键函数
    $keyFunctions = [
        'xn.htmlspecialchars',
        'xn.urlencode',
        'xn.json_encode',
        'xn.time',
        'xn.md5',
        'xn.array_merge',
        'xn.intval'
    ];
    
    $missingFunctions = [];
    foreach ($keyFunctions as $func) {
        if (strpos($content, $func) !== false) {
            echo "✓ $func 函数存在\n";
        } else {
            echo "✗ $func 函数缺失\n";
            $missingFunctions[] = $func;
        }
    }
    
    // 检查加载信息
    if (strpos($content, 'boyou.js loaded') !== false) {
        echo "✓ 包含正确的加载信息\n";
    } elseif (strpos($content, 'xiuno.js loaded') !== false) {
        echo "⚠️  仍包含xiuno.js加载信息\n";
    } else {
        echo "⚠️  缺少加载信息\n";
    }
    
} else {
    echo "✗ boyou.js 文件不存在\n";
}

// 3. 检查HTML模板中的JS引用
echo "\n3. 检查HTML模板中的JS引用...\n";

$templateFiles = [
    'view/htm/footer.inc.htm',
    'admin/view/htm/footer.inc.htm',
    'install/view/htm/footer.inc.htm',
    'tmp/view_htm_footer.inc.htm',
    'tmp/admin_view_htm_footer.inc.htm'
];

$templateIssues = [];
foreach ($templateFiles as $file) {
    if (!file_exists($file)) {
        echo "⚠️  模板文件不存在: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    if (strpos($content, 'boyou.js') !== false) {
        echo "✓ $file 正确引用 boyou.js\n";
    } elseif (strpos($content, 'xiuno.js') !== false) {
        echo "⚠️  $file 仍引用 xiuno.js\n";
        $templateIssues[] = "$file 需要更新JS引用";
    } else {
        echo "- $file 无JS引用\n";
    }
}

// 4. 创建JavaScript测试页面
echo "\n4. 创建JavaScript测试页面...\n";

$testPageContent = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS JavaScript 功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { margin: 5px; padding: 8px 16px; }
    </style>
</head>
<body>
    <h1>Boyou BBS 6.1 JavaScript 功能测试</h1>
    
    <div id="test-results"></div>
    
    <h2>手动测试</h2>
    <button onclick="testBasicFunctions()">测试基本函数</button>
    <button onclick="testStringFunctions()">测试字符串函数</button>
    <button onclick="testArrayFunctions()">测试数组函数</button>
    <button onclick="testUtilityFunctions()">测试工具函数</button>
    <button onclick="runAllTests()">运行所有测试</button>
    
    <script src="js/boyou.js"></script>
    <script>
        function addResult(message, type = "info") {
            const div = document.createElement("div");
            div.className = "test-result " + type;
            div.textContent = message;
            document.getElementById("test-results").appendChild(div);
        }
        
        function testBasicFunctions() {
            addResult("=== 测试基本函数 ===", "info");
            
            try {
                // 测试时间函数
                const time = xn.time();
                if (time > 0) {
                    addResult("✓ xn.time() 工作正常: " + time, "success");
                } else {
                    addResult("✗ xn.time() 工作异常", "error");
                }
                
                // 测试JSON函数
                const obj = {name: "Boyou BBS", version: "6.1"};
                const json = xn.json_encode(obj);
                const decoded = xn.json_decode(json);
                
                if (decoded.name === "Boyou BBS") {
                    addResult("✓ JSON编解码函数工作正常", "success");
                } else {
                    addResult("✗ JSON编解码函数工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 基本函数测试出错: " + e.message, "error");
            }
        }
        
        function testStringFunctions() {
            addResult("=== 测试字符串函数 ===", "info");
            
            try {
                // 测试字符串函数
                const str = "Hello Boyou BBS";
                
                if (xn.strlen(str) === 15) {
                    addResult("✓ xn.strlen() 工作正常", "success");
                } else {
                    addResult("✗ xn.strlen() 工作异常", "error");
                }
                
                if (xn.strtolower(str) === "hello boyou bbs") {
                    addResult("✓ xn.strtolower() 工作正常", "success");
                } else {
                    addResult("✗ xn.strtolower() 工作异常", "error");
                }
                
                if (xn.strtoupper(str) === "HELLO BOYOU BBS") {
                    addResult("✓ xn.strtoupper() 工作正常", "success");
                } else {
                    addResult("✗ xn.strtoupper() 工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 字符串函数测试出错: " + e.message, "error");
            }
        }
        
        function testArrayFunctions() {
            addResult("=== 测试数组函数 ===", "info");
            
            try {
                const arr1 = [1, 2, 3];
                const arr2 = [4, 5, 6];
                const merged = xn.array_merge(arr1, arr2);
                
                if (merged.length === 6 && merged[5] === 6) {
                    addResult("✓ xn.array_merge() 工作正常", "success");
                } else {
                    addResult("✗ xn.array_merge() 工作异常", "error");
                }
                
                const obj = {a: 1, b: 2, c: 3};
                const keys = xn.array_keys(obj);
                const values = xn.array_values(obj);
                
                if (keys.length === 3 && values.length === 3) {
                    addResult("✓ xn.array_keys() 和 xn.array_values() 工作正常", "success");
                } else {
                    addResult("✗ 数组键值函数工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 数组函数测试出错: " + e.message, "error");
            }
        }
        
        function testUtilityFunctions() {
            addResult("=== 测试工具函数 ===", "info");
            
            try {
                // 测试数值函数
                if (xn.intval("123") === 123) {
                    addResult("✓ xn.intval() 工作正常", "success");
                } else {
                    addResult("✗ xn.intval() 工作异常", "error");
                }
                
                if (xn.floatval("123.45") === 123.45) {
                    addResult("✓ xn.floatval() 工作正常", "success");
                } else {
                    addResult("✗ xn.floatval() 工作异常", "error");
                }
                
                // 测试类型检查函数
                if (xn.is_array([1,2,3]) && !xn.is_array("string")) {
                    addResult("✓ xn.is_array() 工作正常", "success");
                } else {
                    addResult("✗ xn.is_array() 工作异常", "error");
                }
                
                if (xn.is_string("test") && !xn.is_string(123)) {
                    addResult("✓ xn.is_string() 工作正常", "success");
                } else {
                    addResult("✗ xn.is_string() 工作异常", "error");
                }
                
            } catch (e) {
                addResult("✗ 工具函数测试出错: " + e.message, "error");
            }
        }
        
        function runAllTests() {
            document.getElementById("test-results").innerHTML = "";
            addResult("开始运行所有JavaScript测试...", "info");
            
            testBasicFunctions();
            testStringFunctions();
            testArrayFunctions();
            testUtilityFunctions();
            
            addResult("所有测试完成！", "info");
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            addResult("Boyou BBS JavaScript 测试页面已加载", "info");
            if (typeof xn !== "undefined") {
                addResult("✓ boyou.js 已成功加载", "success");
            } else {
                addResult("✗ boyou.js 加载失败", "error");
            }
        };
    </script>
</body>
</html>';

if (file_put_contents('js_test.html', $testPageContent)) {
    echo "✓ JavaScript测试页面创建成功: js_test.html\n";
} else {
    echo "✗ JavaScript测试页面创建失败\n";
}

// 5. 生成测试报告
echo "\n=== JavaScript测试报告 ===\n";

$issues = [];

if (!$jsStatus['view/js/boyou.js']) {
    $issues[] = "boyou.js 文件不存在";
}

if ($jsStatus['view/js/xiuno.js']) {
    $issues[] = "xiuno.js 文件仍然存在，建议删除";
}

if (!empty($missingFunctions)) {
    $issues[] = "boyou.js 缺少函数: " . implode(', ', $missingFunctions);
}

if (!empty($templateIssues)) {
    $issues[] = "模板文件问题: " . implode(', ', $templateIssues);
}

if (empty($issues)) {
    echo "🎉 JavaScript功能测试全部通过！\n";
    echo "\n✅ 测试通过项目:\n";
    echo "  - boyou.js 文件存在且完整\n";
    echo "  - 所有关键函数都存在\n";
    echo "  - 模板文件正确引用boyou.js\n";
    echo "  - 测试页面创建成功\n";
} else {
    echo "⚠️  发现以下问题:\n";
    foreach ($issues as $issue) {
        echo "  - $issue\n";
    }
}

echo "\n💡 下一步建议:\n";
echo "  - 在浏览器中打开 js_test.html 进行手动测试\n";
echo "  - 测试网站前端交互功能\n";
if ($jsStatus['view/js/xiuno.js']) {
    echo "  - 删除不再需要的 xiuno.js 文件\n";
}

echo "\n=== JavaScript测试完成 ===\n";

?>
