<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 - 测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        .content {
            padding: 40px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #28a745;
        }
        .status-card.warning {
            border-left-color: #ffc107;
        }
        .status-card.error {
            border-left-color: #dc3545;
        }
        .status-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
        }
        .badge.success {
            background: #d4edda;
            color: #155724;
        }
        .badge.warning {
            background: #fff3cd;
            color: #856404;
        }
        .badge.error {
            background: #f8d7da;
            color: #721c24;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        .feature-card h4 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .feature-card p {
            margin: 0;
            opacity: 0.9;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Boyou BBS 6.1</h1>
            <p>现代化论坛系统 - 测试页面</p>
        </div>
        
        <div class="content">
            <div class="status-grid">
                <div class="status-card">
                    <h3>🔧 系统状态</h3>
                    <div class="status-item">
                        <span>PHP版本</span>
                        <span class="badge success"><?php echo PHP_VERSION; ?></span>
                    </div>
                    <div class="status-item">
                        <span>服务器时间</span>
                        <span class="badge success"><?php echo date('Y-m-d H:i:s'); ?></span>
                    </div>
                    <div class="status-item">
                        <span>内存使用</span>
                        <span class="badge success"><?php echo round(memory_get_usage()/1024/1024, 2); ?>MB</span>
                    </div>
                </div>
                
                <div class="status-card">
                    <h3>📦 PHP扩展</h3>
                    <div class="status-item">
                        <span>PDO</span>
                        <span class="badge <?php echo extension_loaded('pdo') ? 'success' : 'error'; ?>">
                            <?php echo extension_loaded('pdo') ? '✓ 已安装' : '✗ 未安装'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>JSON</span>
                        <span class="badge <?php echo extension_loaded('json') ? 'success' : 'error'; ?>">
                            <?php echo extension_loaded('json') ? '✓ 已安装' : '✗ 未安装'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>mbstring</span>
                        <span class="badge <?php echo extension_loaded('mbstring') ? 'success' : 'error'; ?>">
                            <?php echo extension_loaded('mbstring') ? '✓ 已安装' : '✗ 未安装'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>GD</span>
                        <span class="badge <?php echo extension_loaded('gd') ? 'success' : 'warning'; ?>">
                            <?php echo extension_loaded('gd') ? '✓ 已安装' : '⚠ 未安装'; ?>
                        </span>
                    </div>
                </div>
                
                <div class="status-card">
                    <h3>📁 目录权限</h3>
                    <div class="status-item">
                        <span>upload/</span>
                        <span class="badge <?php echo is_writable('upload') ? 'success' : 'error'; ?>">
                            <?php echo is_writable('upload') ? '✓ 可写' : '✗ 不可写'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>tmp/</span>
                        <span class="badge <?php echo is_writable('tmp') ? 'success' : 'error'; ?>">
                            <?php echo is_writable('tmp') ? '✓ 可写' : '✗ 不可写'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>log/</span>
                        <span class="badge <?php echo is_writable('log') ? 'success' : 'error'; ?>">
                            <?php echo is_writable('log') ? '✓ 可写' : '✗ 不可写'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>data/</span>
                        <span class="badge <?php echo is_writable('data') ? 'success' : 'error'; ?>">
                            <?php echo is_writable('data') ? '✓ 可写' : '✗ 不可写'; ?>
                        </span>
                    </div>
                </div>
                
                <div class="status-card">
                    <h3>🛡️ 安全功能</h3>
                    <div class="status-item">
                        <span>CSRF保护</span>
                        <span class="badge <?php echo function_exists('csrf_token_generate') ? 'success' : 'warning'; ?>">
                            <?php echo function_exists('csrf_token_generate') ? '✓ 已启用' : '⚠ 未启用'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>输入过滤</span>
                        <span class="badge <?php echo function_exists('xn_input_filter') ? 'success' : 'warning'; ?>">
                            <?php echo function_exists('xn_input_filter') ? '✓ 已启用' : '⚠ 未启用'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span>安全输出</span>
                        <span class="badge <?php echo function_exists('safe_echo') ? 'success' : 'warning'; ?>">
                            <?php echo function_exists('safe_echo') ? '✓ 已启用' : '⚠ 未启用'; ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🎨 现代化设计</h4>
                    <p>响应式设计，完美适配各种设备</p>
                </div>
                <div class="feature-card">
                    <h4>🛡️ 企业级安全</h4>
                    <p>CSRF保护、XSS防护、输入验证</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ 高性能</h4>
                    <p>支持Redis、Memcached等缓存</p>
                </div>
                <div class="feature-card">
                    <h4>🔧 易于扩展</h4>
                    <p>强大的插件系统和主题支持</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="install/" class="btn">🚀 开始安装</a>
                <a href="health_check.php" class="btn">🏥 健康检查</a>
                <a href="js_test.html" class="btn">🧪 JavaScript测试</a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Boyou BBS 6.1</strong> &copy; 2024 | 基于 BoyouPHP 6.1 框架</p>
            <p>现代化论坛系统，为社区交流而生</p>
        </div>
    </div>
</body>
</html>
