<?php

// 启用所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 测试路由文件 ===\n";

try {
    // 设置基本环境
    define('DEBUG', 1);
    define('APP_PATH', __DIR__.'/');
    define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');
    
    // 加载配置
    $conf = include APP_PATH.'conf/conf.php';
    $_SERVER['conf'] = $conf;
    
    // 加载框架
    include BOYOUPHP_PATH.'boyouphp.php';
    
    // 加载模型
    include _include(APP_PATH.'model.inc.php');
    
    // 模拟会话和用户
    $_SESSION = array();
    $uid = 0;
    $user = null;
    $gid = 3; // 默认会员组
    
    // 加载组列表
    $grouplist = group_list_cache();
    $group = isset($grouplist[$gid]) ? $grouplist[$gid] : reset($grouplist);

    // 加载版块列表
    $forumlist = forum_list_cache();
    $forumlist_show = forum_list_access_filter($forumlist, $gid);

    // 设置必要的变量
    $_REQUEST = array();
    $route = 'index';
    
    echo "环境设置完成，开始测试路由...\n";
    
    // 测试路由文件
    echo "包含 route/index.php...\n";
    include APP_PATH.'route/index.php';
    echo "路由文件执行完成\n";
    
} catch (Error $e) {
    echo "\n致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "\n异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";

?>
