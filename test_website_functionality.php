<?php

echo "=== 网站功能测试 ===\n\n";

// 1. 测试框架加载
echo "1. 测试框架加载...\n";
try {
    // 设置必要的常量
    define('DEBUG', 1);
    define('APP_PATH', './');
    define('BOYOUPHP_PATH', './boyouphp/');
    
    // 包含框架
    include_once 'boyouphp/boyouphp.php';
    
    if (defined('BOYOUPHP_VERSION')) {
        echo "✓ BoyouPHP 框架加载成功，版本: " . BOYOUPHP_VERSION . "\n";
    } else {
        echo "✗ 框架版本常量未定义\n";
    }
    
} catch (Exception $e) {
    echo "✗ 框架加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 测试配置文件
echo "\n2. 测试配置文件...\n";
if (file_exists('conf/conf.php')) {
    try {
        $conf = include 'conf/conf.php';
        if (is_array($conf)) {
            echo "✓ 配置文件加载成功\n";
            echo "  站点名称: " . ($conf['sitename'] ?? '未设置') . "\n";
            echo "  版本号: " . ($conf['version'] ?? '未设置') . "\n";
            echo "  数据库配置: " . (isset($conf['db']) ? '已配置' : '未配置') . "\n";
        } else {
            echo "✗ 配置文件格式错误\n";
        }
    } catch (Exception $e) {
        echo "✗ 配置文件加载失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ 配置文件不存在\n";
}

// 3. 测试核心函数
echo "\n3. 测试核心函数...\n";

// 测试全局函数
if (function_exists('G')) {
    G('test_key', 'test_value');
    if (G('test_key') === 'test_value') {
        echo "✓ 全局函数 G() 工作正常\n";
    } else {
        echo "✗ 全局函数 G() 工作异常\n";
    }
} else {
    echo "✗ 全局函数 G() 不存在\n";
}

// 测试参数函数
if (function_exists('param')) {
    $_GET['test_param'] = 'test_value';
    $value = param('test_param', 'default');
    if ($value === 'test_value') {
        echo "✓ 参数函数 param() 工作正常\n";
    } else {
        echo "✗ 参数函数 param() 工作异常\n";
    }
} else {
    echo "✗ 参数函数 param() 不存在\n";
}

// 测试加密函数
if (function_exists('xn_encrypt') && function_exists('xn_decrypt')) {
    $testData = 'Hello Boyou BBS 6.1';
    $encrypted = xn_encrypt($testData, 'test_key');
    $decrypted = xn_decrypt($encrypted, 'test_key');
    
    if ($decrypted === $testData) {
        echo "✓ 加密解密函数工作正常\n";
    } else {
        echo "✗ 加密解密函数工作异常\n";
    }
} else {
    echo "✗ 加密解密函数不存在\n";
}

// 测试JSON函数
if (function_exists('xn_json_encode') && function_exists('xn_json_decode')) {
    $testArray = ['name' => 'Boyou BBS', 'version' => '6.1'];
    $json = xn_json_encode($testArray);
    $decoded = xn_json_decode($json);
    
    if ($decoded['name'] === 'Boyou BBS') {
        echo "✓ JSON编解码函数工作正常\n";
    } else {
        echo "✗ JSON编解码函数工作异常\n";
    }
} else {
    echo "✗ JSON编解码函数不存在\n";
}

// 4. 测试数据库函数
echo "\n4. 测试数据库函数...\n";
$dbFunctions = ['db_new', 'db_connect', 'db_query', 'db_exec', 'db_escape'];
$missingDbFunctions = [];

foreach ($dbFunctions as $func) {
    if (function_exists($func)) {
        echo "✓ $func() 函数存在\n";
    } else {
        echo "✗ $func() 函数不存在\n";
        $missingDbFunctions[] = $func;
    }
}

if (empty($missingDbFunctions)) {
    echo "✓ 所有数据库函数都存在\n";
} else {
    echo "⚠️  缺少 " . count($missingDbFunctions) . " 个数据库函数\n";
}

// 5. 测试缓存函数
echo "\n5. 测试缓存函数...\n";
$cacheFunctions = ['cache_new', 'cache_set', 'cache_get', 'cache_delete'];
$missingCacheFunctions = [];

foreach ($cacheFunctions as $func) {
    if (function_exists($func)) {
        echo "✓ $func() 函数存在\n";
    } else {
        echo "✗ $func() 函数不存在\n";
        $missingCacheFunctions[] = $func;
    }
}

if (empty($missingCacheFunctions)) {
    echo "✓ 所有缓存函数都存在\n";
} else {
    echo "⚠️  缺少 " . count($missingCacheFunctions) . " 个缓存函数\n";
}

// 6. 测试模板文件
echo "\n6. 测试模板文件...\n";
$templateFiles = [
    'view/htm/header.inc.htm',
    'view/htm/footer.inc.htm',
    'view/htm/footer_nav.inc.htm',
    'admin/view/htm/header.inc.htm',
    'admin/view/htm/footer.inc.htm'
];

$templateIssues = [];
foreach ($templateFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // 检查是否引用了boyou.js
        if (strpos($content, 'boyou.js') !== false) {
            echo "✓ $file 正确引用 boyou.js\n";
        } elseif (strpos($content, 'boyou.js') !== false) {
            echo "⚠️  $file 仍然引用 boyou.js\n";
            $templateIssues[] = "$file 需要更新JS引用";
        } else {
            echo "- $file 无JS引用\n";
        }
        
        // 检查品牌信息
        if (strpos($content, 'Boyou BBS') !== false) {
            echo "✓ $file 包含 Boyou BBS 品牌\n";
        } elseif (strpos($content, 'Boyou BBS') !== false) {
            echo "⚠️  $file 仍包含 Boyou BBS 品牌\n";
            $templateIssues[] = "$file 需要更新品牌信息";
        }
        
    } else {
        echo "✗ $file 不存在\n";
        $templateIssues[] = "$file 文件缺失";
    }
}

// 7. 生成测试报告
echo "\n=== 功能测试报告 ===\n";

$totalIssues = count($missingDbFunctions) + count($missingCacheFunctions) + count($templateIssues);

if ($totalIssues === 0) {
    echo "🎉 所有功能测试都通过了！\n";
    echo "\n✅ 测试通过项目:\n";
    echo "  - BoyouPHP 框架正常加载\n";
    echo "  - 配置文件正常工作\n";
    echo "  - 核心函数正常工作\n";
    echo "  - 数据库函数完整\n";
    echo "  - 缓存函数完整\n";
    echo "  - 模板文件正确更新\n";
} else {
    echo "⚠️  发现 $totalIssues 个问题需要处理\n";
    
    if (!empty($missingDbFunctions)) {
        echo "\n缺少的数据库函数:\n";
        foreach ($missingDbFunctions as $func) {
            echo "  - $func\n";
        }
    }
    
    if (!empty($missingCacheFunctions)) {
        echo "\n缺少的缓存函数:\n";
        foreach ($missingCacheFunctions as $func) {
            echo "  - $func\n";
        }
    }
    
    if (!empty($templateIssues)) {
        echo "\n模板文件问题:\n";
        foreach ($templateIssues as $issue) {
            echo "  - $issue\n";
        }
    }
}

echo "\n=== 功能测试完成 ===\n";

?>
