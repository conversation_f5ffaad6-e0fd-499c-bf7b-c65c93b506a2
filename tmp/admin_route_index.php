<?php

!defined('DEBUG') AND exit('Access Denied.');

$action = param(1);



if($action == 'login') {

	
	
	if($method == 'GET') {

		
		
		$header['title'] = lang('admin_login');
		
		include _include(ADMIN_PATH."view/htm/index_login.htm");

	} else if($method == 'POST') {

		

		$password = param('password');

		// 验证 CSRF 令牌
		!csrf_token_verify() AND message(-1, 'CSRF 验证失败');

		// 管理员登录只验证当前已登录用户的密码
		// 确保用户已经登录且是管理员
		empty($user) AND message('password', '请先登录普通用户账号');
		!user_is_admin($user['gid']) AND message('password', '您不是管理员');

		// 使用新的安全密码验证
		if(!user_password_verify($password, $user['password'], $user['salt'])) {
			xn_log('admin password error. uid:'.$user['uid'].' - ******'.substr($password, -6), 'admin_login_error');
			message('password', lang('password_incorrect'));
		}

		// 如果密码正确但使用旧格式，升级密码哈希
		if (user_password_needs_rehash($user['password'])) {
			$new_hash = user_password_hash($password);
			user_update($user['uid'], array('password' => $new_hash, 'salt' => ''));
		}

		admin_token_set();

		xn_log('admin login successed. uid:'.$user['uid'], 'admin_login');

		

		message(0, jump(lang('login_successfully'), '.'));

	}

} elseif ($action == 'logout') {

	
	
	admin_token_clean();
	
	message(0, jump(lang('logout_successfully'), './'));

} elseif ($action == 'phpinfo') {
	
	unset($_SERVER['conf']);
	unset($_SERVER['db']);
	unset($_SERVER['cache']);
	phpinfo();
	exit;
	
} else {

	
	
	$header['title'] = lang('admin_page');
	
	$info = array();
	$info['disable_functions'] = ini_get('disable_functions');
	$info['allow_url_fopen'] = ini_get('allow_url_fopen') ? lang('yes') : lang('no');
	$info['safe_mode'] = ini_get('safe_mode') ? lang('yes') : lang('no');
	empty($info['disable_functions']) && $info['disable_functions'] = lang('none');
	$info['upload_max_filesize'] = ini_get('upload_max_filesize');
	$info['post_max_size'] = ini_get('post_max_size');
	$info['memory_limit'] = ini_get('memory_limit');
	$info['max_execution_time'] = ini_get('max_execution_time');
	$info['dbversion'] = $db->version();
	$info['SERVER_SOFTWARE'] = _SERVER('SERVER_SOFTWARE');
	$info['HTTP_X_FORWARDED_FOR'] = _SERVER('HTTP_X_FORWARDED_FOR');
	$info['REMOTE_ADDR'] = _SERVER('REMOTE_ADDR');
	
	
	$stat = array();
	$stat['threads'] = thread_count();
	$stat['posts'] = post_count();
	$stat['users'] = user_count();
	$stat['attachs'] = attach_count();
	$stat['disk_free_space'] = function_exists('disk_free_space') ? humansize(disk_free_space(APP_PATH)) : lang('unknown');
	
	$lastversion = get_last_version($stat);
	
	
	
	include _include(ADMIN_PATH.'view/htm/index.htm');

}



function get_last_version($stat) {
	global $conf, $time;
	$last_version = kv_get('last_version');
	if($time - $last_version > 86400) {
		kv_set('last_version', $time);
		$sitename = urlencode($conf['sitename']);
		$sitedomain = urlencode(http_url_path());
		$version = urlencode($conf['version']);
		return '<script src="http://custom.xiuno.com/version.htm?sitename='.$sitename.'&sitedomain='.$sitedomain.'&users='.$stat['users'].'&threads='.$stat['threads'].'&posts='.$stat['posts'].'&version='.$version.'"></script>';
	} else {
		return '';
	}
}

?>
