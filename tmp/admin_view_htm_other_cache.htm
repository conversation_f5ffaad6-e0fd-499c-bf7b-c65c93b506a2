<?php include _include(ADMIN_PATH.'view/htm/header.inc.htm');?>



<div class="row">
	<div class="col-12">
		<div class="btn-group mb-3" role="group">
			<?php echo admin_tab_active($menu['other']['tab'], 'cache');?>
		</div>
		<div class="card">
			<div class="card-body">
				<!--
				<h4 class="card-title"><?php echo lang('admin_other_cache');?></h4>
				-->
				<form action="<?php echo url('other-cache');?>" method="post" id="form">
				
					<div class="form-group row">
						<label class="col-sm-6 form-control-label"><?php echo lang('admin_clear_tmp');?>：</label>
						<div class="col-sm-6">
							<?php echo $input['clear_tmp'];?>
						</div>
					</div>
					
					<div class="form-group row">
						<label class="col-sm-6 form-control-label"><?php echo lang('admin_clear_cache');?>：</label>
						<div class="col-sm-6">
							<?php echo $input['clear_cache'];?>
						</div>
					</div>
								
					<div class="form-group row">
						<label for="inputPassword3" class="col-sm-2 form-control-label"></label>
						<div class="col-sm-10">
							<button type="submit" class="btn btn-primary btn-block" id="submit" data-loading-text="<?php echo lang('submiting');?>..."><?php echo lang('confirm');?></button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


	
<?php include _include(ADMIN_PATH.'view/htm/footer.inc.htm');?>

<script>
var jform = $('#form');
var jsubmit = $('#submit');
jform.on('submit', function() {
	jform.reset();
	jsubmit.button('loading');
	var postdata = jform.serialize();
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			$.alert(message);
			jsubmit.text(message).delay(1000).button('reset');
		} else if(xn.is_number(code)) {
			alert(message);
			jsubmit.button('reset');
		} else {
			jform.find('[name="'+code+'"]').alert(message).focus();
			jsubmit.button('reset');
		}
	});
	return false;
});

$('#nav li.nav-item-other').addClass('active');

</script>


