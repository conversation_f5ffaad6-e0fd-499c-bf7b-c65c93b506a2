<?php include _include(ADMIN_PATH.'view/htm/header.inc.htm');?>



<div class="row">
	<div class="col-lg-8 mx-auto">
		<div class="btn-group mb-3" role="group">
			<?php echo admin_tab_active($menu['user']['tab'], 'create');?>
		</div>
		<div class="card">
			<div class="card-body">
				<!--
				<h4 class="card-title"><?php echo lang('create_user');?></h4>
				-->
				<form action="<?php echo url("user-create");?>" method="post" id="form">
					<div class="form-group row">
						<label class="col-sm-2 form-control-label"><?php echo lang('email');?>：</label>
						<div class="col-sm-10">
							<?php echo $input['email'];?>
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2 form-control-label"><?php echo lang('username');?>：</label>
						<div class="col-sm-10">
							<?php echo $input['username'];?>
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2 form-control-label"><?php echo lang('password');?>：</label>
						<div class="col-sm-10">
							<?php echo $input['password'];?>
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2 form-control-label"><?php echo lang('user_group');?>：</label>
						<div class="col-sm-10">
							<?php echo $input['_gid'];?>
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2 form-control-label"></label>
						<div class="col-sm-10">
							<button type="submit" class="btn btn-primary btn-block" id="submit" data-loading-text="<?php echo lang('submiting');?>..."><?php echo lang('confirm');?></button>
							<a role="button" class="btn btn-secondary btn-block mt-3" href="javascript:history.back();"><?php echo lang('back');?></a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>



<?php include _include(ADMIN_PATH.'view/htm/footer.inc.htm');?>


<script>
var jform = $('#form');
var jsubmit = $('#submit');
jform.on('submit', function() {
	jform.reset();
	jsubmit.button('loading');
	var postdata = jform.serialize();
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			$.alert(message);
			jsubmit.text(message).delay(1000).location(xn.url('user-list'));
		} else if(xn.is_number(code)) {
			alert(message);
			jsubmit.button('reset');
		} else {
			jform.find('[name="'+code+'"]').alert(message).focus();
			jsubmit.button('reset');
		}
	});
	return false;
});

$('#nav li.nav-item-user').addClass('active');

</script>


