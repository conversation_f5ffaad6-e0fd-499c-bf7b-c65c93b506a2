<?php include _include(ADMIN_PATH.'view/htm/header.inc.htm');?>



<div class="row">
	<div class="col-12">
		<div class="btn-group mb-3" role="group">
			<?php echo admin_tab_active($menu['user']['tab'], 'list');?>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-2 mb-3">
		<select id="srchtype" value="<?php echo $srchtype; ?>" class="form-control">
			<option value=""><?php echo lang('search_type');?></option>
			<option value="uid"><?php echo lang('uid');?>：</option>
			<option value="username" selected><?php echo lang('username');?>：</option>
			<option value="email"><?php echo lang('email');?>：</option>
			<option value="gid"><?php echo lang('user_group');?>：</option>
			<option value="create_ip"><?php echo lang('create_ip');?>：</option>
			
		</select>
	</div>
	<div class="col-md-3 mb-3">
		<input type="text" id="keyword" placeholder="<?php echo lang('keyword');?>" value="<?php echo $keyword; ?>"  class="form-control" />
	</div>
	<div class="col-md-2 mb-3">
		<button id="search" class="form-control btn btn-primary"><?php echo lang('search');?></button>
	</div>
</div>

<div class="row">
	<div class="col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table class="table" style="min-width: 800px">
						<thead>
							<tr>
								<th width="60" class="text-center"><?php echo lang('delete');?></th>
								<th width="50">ID：</th>
								
								<th width="150"><?php echo lang('email');?></th>
								<th width="150"><?php echo lang('username');?></th>
								<th width="100"><?php echo lang('user_group');?></th>
								<th width="100"><?php echo lang('create_date');?></th>
								<th width="100"><?php echo lang('create_ip');?></th>
								<th width="60"><?php echo lang('operation');?></th>
							</tr>
						</thead>
						<tbody id="userlist">
					<?php foreach($userlist as &$_user){ ?>
						<tr uid="<?php echo $_user['uid']; ?>">
							<td class="text-center"><input type="checkbox" name="delete" /></td>
							<td><?php echo $_user['uid']; ?></td>
							
							<td><?php echo $_user['email']; ?></td>
							<td><?php echo $_user['username']; ?></td>
							<td><?php echo $_user['groupname']; ?></td>
							<td><?php echo date('Y-n-j H:i:s', $_user['create_date']); ?></td>
							<td><a href="https://bbs.xiuno.com/ip.htm?ip=<?php echo $_user['create_ip_fmt']; ?>" target="_blank"><?php echo $_user['create_ip_fmt']; ?></a></td>
							<td><a href="<?php echo url("user-update-$_user[uid]"); ?>"><?php echo lang('edit');?></a></td>
						</tr>
					<?php } ?>
						</tbody>
					</table>
				</div>
				<hr class="mt-0" />
				<p><button class="btn btn-sm ml-3" id="confirm"><?php echo lang('delete');?></button></p>
			</div>
		</div>
		<nav><ul class="pagination justify-content-center"><?php echo $pagination; ?></ul></nav>
	</div>
</div>



<?php include _include(ADMIN_PATH.'view/htm/footer.inc.htm');?>

<script>
var jbody = $("#userbody");
var jcreate = $('#create');
var jconfirm = $("#confirm");
var jsearch = $("#search");
var jsrchtype = $("#srchtype");
var jkeyword = $('#keyword');
var juserlist = $('#userlist');
jsrchtype.val(jsrchtype.attr('value'));

jcreate.on('click', function(event) {
	window.location = xn.url('user-create');
});

jsearch.on('click', function(){
	var srchtype = jsrchtype.val();
	srchtype = srchtype ? srchtype : 'uid';
	var keyword = $("#keyword").val();
	var url = xn.url('user-list-'+srchtype+'-'+xn.urlencode(keyword)+'-1');
	window.location = url;
});
jkeyword.on('keydown', function(e) {
	if(e.keyCode == 13) jsearch.trigger('click');
});

// 删除选中的用户
jconfirm.on('click', function(){
	var jchecked = juserlist.find('input[name="delete"]').filter(function(){ return this.checked; });
	if(jchecked.length < 1) return alert("<?php echo lang('please_check_delete_user');?>");
	if(!window.confirm("<?php echo lang('user_delete_confirm');?>")) return false;
	jchecked.each(function() {
		jtr = $(this).parents('tr');
		var uid = jtr.attr('uid');
		if(uid == 1) return alert('<?php echo lang('user_admin_cant_be_deleted');?>');
		$.xpost(xn.url('user-delete'), {uid:uid}, function(code, message) {
			if(code != 0) return $.alert(message);
			jtr.remove();
		});
	});
});


$('#nav li.nav-item-user').addClass('active');

</script>


