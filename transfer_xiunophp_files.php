<?php

echo "=== 转移 xiunophp 文件到 boyouphp ===\n\n";

$sourceDir = 'xiunophp';
$targetDir = 'boyouphp';

// 确保目标目录存在
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0755, true);
    echo "✓ 创建目标目录: $targetDir\n";
}

// 获取源目录中的所有文件
if (!is_dir($sourceDir)) {
    echo "✗ 源目录不存在: $sourceDir\n";
    exit(1);
}

$files = scandir($sourceDir);
$transferredFiles = [];
$skippedFiles = [];
$renamedFiles = [];

echo "开始转移文件...\n\n";

foreach ($files as $file) {
    if ($file === '.' || $file === '..') continue;
    
    $sourcePath = $sourceDir . '/' . $file;
    
    // 跳过目录
    if (is_dir($sourcePath)) {
        echo "跳过目录: $file\n";
        continue;
    }
    
    // 确定目标文件名
    $targetFileName = $file;
    
    // 重命名规则
    if ($file === 'xiunophp.php') {
        $targetFileName = 'boyouphp.php';
        $renamedFiles[] = "$file → $targetFileName";
    } elseif ($file === 'xiunophp.min.php') {
        $targetFileName = 'boyouphp.min.php';
        $renamedFiles[] = "$file → $targetFileName";
    }
    
    $targetPath = $targetDir . '/' . $targetFileName;
    
    // 检查目标文件是否已存在
    if (file_exists($targetPath)) {
        // 比较文件大小和修改时间
        $sourceSize = filesize($sourcePath);
        $targetSize = filesize($targetPath);
        $sourceMtime = filemtime($sourcePath);
        $targetMtime = filemtime($targetPath);
        
        if ($sourceSize === $targetSize && $sourceMtime <= $targetMtime) {
            echo "跳过已存在的文件: $targetFileName\n";
            $skippedFiles[] = $targetFileName;
            continue;
        }
    }
    
    // 复制文件
    if (copy($sourcePath, $targetPath)) {
        echo "✓ 转移: $file → $targetFileName\n";
        $transferredFiles[] = $targetFileName;
    } else {
        echo "✗ 转移失败: $file\n";
    }
}

echo "\n=== 转移结果 ===\n";
echo "成功转移: " . count($transferredFiles) . " 个文件\n";
echo "跳过文件: " . count($skippedFiles) . " 个文件\n";

if (!empty($renamedFiles)) {
    echo "\n重命名文件:\n";
    foreach ($renamedFiles as $rename) {
        echo "  $rename\n";
    }
}

if (!empty($transferredFiles)) {
    echo "\n转移的文件:\n";
    foreach ($transferredFiles as $file) {
        echo "  $file\n";
    }
}

if (!empty($skippedFiles)) {
    echo "\n跳过的文件:\n";
    foreach ($skippedFiles as $file) {
        echo "  $file\n";
    }
}

// 验证关键文件
echo "\n=== 验证关键文件 ===\n";
$keyFiles = [
    'boyouphp.php',
    'boyouphp.min.php',
    'db.func.php',
    'cache.func.php',
    'misc.func.php',
    'array.func.php',
    'image.func.php'
];

$missingFiles = [];
foreach ($keyFiles as $file) {
    $path = $targetDir . '/' . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✓ $file (" . number_format($size) . " 字节)\n";
    } else {
        echo "✗ $file 缺失\n";
        $missingFiles[] = $file;
    }
}

if (empty($missingFiles)) {
    echo "\n🎉 所有关键文件都已成功转移！\n";
} else {
    echo "\n⚠️ 缺失关键文件: " . implode(', ', $missingFiles) . "\n";
}

echo "\n=== 转移完成 ===\n";

?>
