<?php

echo "=== 更新其他文件中对框架的引用 ===\n\n";

// 需要检查和更新的目录
$directories = [
    '.',
    'admin',
    'install', 
    'plugin',
    'tmp',
    'tool'
];

// 需要更新的引用映射
$replacements = [
    'BOYOUPHP_PATH' => 'BOYOUPHP_PATH',
    'boyouphp/' => 'boyouphp/',
    'boyouphp\\' => 'boyouphp\\',
    "'boyouphp'" => "'boyouphp'",
    '"boyouphp"' => '"boyouphp"',
    'boyouphp.php' => 'boyouphp.php',
    'boyouphp.min.php' => 'boyouphp.min.php',
    'boyou.js' => 'boyou.js',
    'Boyou BBS' => 'Boyou BBS',
    'BOYOU BBS' => 'BOYOU BBS',
    'boyou BBS' => 'boyou BBS'
];

$updatedFiles = [];
$errorFiles = [];
$totalReplacements = 0;

echo "开始扫描和更新文件...\n\n";

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        echo "跳过不存在的目录: $dir\n";
        continue;
    }
    
    echo "处理目录: $dir\n";
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if (!$file->isFile()) continue;
        
        $extension = strtolower($file->getExtension());
        if (!in_array($extension, ['php', 'htm', 'html', 'js', 'css', 'md', 'txt'])) continue;
        
        $filePath = $file->getPathname();
        
        // 跳过boyouphp目录中的文件
        if (strpos($filePath, 'boyouphp/') !== false) continue;
        
        $content = file_get_contents($filePath);
        if ($content === false) continue;
        
        $originalContent = $content;
        $fileReplacements = 0;
        
        // 应用所有替换
        foreach ($replacements as $from => $to) {
            $newContent = str_replace($from, $to, $content);
            if ($newContent !== $content) {
                $count = substr_count($content, $from);
                $fileReplacements += $count;
                $content = $newContent;
                echo "  替换 '$from' → '$to' ($count 次) 在 $filePath\n";
            }
        }
        
        // 如果有更改，保存文件
        if ($content !== $originalContent) {
            if (file_put_contents($filePath, $content) !== false) {
                $updatedFiles[] = $filePath;
                $totalReplacements += $fileReplacements;
                echo "  ✓ 更新文件: $filePath ($fileReplacements 处替换)\n";
            } else {
                $errorFiles[] = $filePath;
                echo "  ✗ 更新失败: $filePath\n";
            }
        }
    }
}

echo "\n=== 更新结果 ===\n";
echo "更新文件数: " . count($updatedFiles) . "\n";
echo "总替换次数: $totalReplacements\n";
echo "失败文件数: " . count($errorFiles) . "\n";

if (!empty($updatedFiles)) {
    echo "\n更新的文件:\n";
    foreach ($updatedFiles as $file) {
        echo "  $file\n";
    }
}

if (!empty($errorFiles)) {
    echo "\n失败的文件:\n";
    foreach ($errorFiles as $file) {
        echo "  $file\n";
    }
}

// 特别检查一些关键文件
echo "\n=== 验证关键文件 ===\n";
$keyFiles = [
    'index.php',
    'install/index.php',
    'tmp/model.min.php',
    'tmp/model.inc.php',
    'view/htm/footer.inc.htm',
    'admin/view/htm/footer.inc.htm'
];

$remainingIssues = [];
foreach ($keyFiles as $file) {
    if (!file_exists($file)) {
        echo "⚠️  文件不存在: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // 检查是否还有xiuno相关引用
    $hasXiuno = false;
    $xiunoMatches = [];
    
    if (preg_match_all('/xiuno|XIUNO|xiunophp|XIUNOPHP/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
        foreach ($matches[0] as $match) {
            // 排除一些合理的保留项
            $text = $match[0];
            if (strpos($text, 'xiuno.com') === false && 
                strpos($text, 'xiuno_') === false) {
                $hasXiuno = true;
                $xiunoMatches[] = $text;
            }
        }
    }
    
    if ($hasXiuno) {
        echo "⚠️  $file 仍包含xiuno引用: " . implode(', ', array_unique($xiunoMatches)) . "\n";
        $remainingIssues[] = $file;
    } else {
        echo "✓ $file 已正确更新\n";
    }
}

echo "\n=== 更新总结 ===\n";

if (empty($remainingIssues) && empty($errorFiles)) {
    echo "🎉 所有框架引用都已成功更新！\n";
    echo "\n主要更新:\n";
    echo "  - BOYOUPHP_PATH → BOYOUPHP_PATH\n";
    echo "  - boyouphp/ → boyouphp/\n";
    echo "  - boyouphp.php → boyouphp.php\n";
    echo "  - boyou.js → boyou.js\n";
    echo "  - Boyou BBS → Boyou BBS\n";
} else {
    echo "⚠️  还有一些问题需要处理:\n";
    
    if (!empty($remainingIssues)) {
        echo "\n仍有xiuno引用的文件:\n";
        foreach ($remainingIssues as $file) {
            echo "  - $file\n";
        }
    }
    
    if (!empty($errorFiles)) {
        echo "\n更新失败的文件:\n";
        foreach ($errorFiles as $file) {
            echo "  - $file\n";
        }
    }
}

echo "\n=== 引用更新完成 ===\n";

?>
