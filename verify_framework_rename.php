<?php

echo "=== 验证框架重命名和版本更新 ===\n\n";

$checks = [];
$errors = [];

// 1. 检查boyouphp目录是否存在
echo "1. 检查 boyouphp 目录...\n";
if (is_dir('boyouphp')) {
    echo "   ✓ boyouphp 目录存在\n";
    $checks['boyouphp_dir'] = true;
} else {
    echo "   ✗ boyouphp 目录不存在\n";
    $errors[] = "boyouphp 目录缺失";
    $checks['boyouphp_dir'] = false;
}

// 2. 检查核心文件
echo "\n2. 检查核心文件...\n";
$coreFiles = [
    'boyouphp/boyouphp.php',
    'boyouphp/boyouphp.min.php',
    'boyouphp/db.func.php',
    'boyouphp/misc.func.php'
];

foreach ($coreFiles as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "   ✓ $file (" . number_format($size) . " 字节)\n";
        $checks[$file] = true;
    } else {
        echo "   ✗ $file 不存在\n";
        $errors[] = "$file 缺失";
        $checks[$file] = false;
    }
}

// 3. 检查版本常量
echo "\n3. 检查版本常量...\n";
if (file_exists('boyouphp/boyouphp.php')) {
    $content = file_get_contents('boyouphp/boyouphp.php');
    if (strpos($content, "define('BOYOUPHP_VERSION', '6.1')") !== false) {
        echo "   ✓ BOYOUPHP_VERSION 常量正确\n";
        $checks['version_constant'] = true;
    } else {
        echo "   ✗ BOYOUPHP_VERSION 常量未找到或不正确\n";
        $errors[] = "版本常量错误";
        $checks['version_constant'] = false;
    }
    
    if (strpos($content, "define('BOYOU_BBS_VERSION', '6.1')") !== false) {
        echo "   ✓ BOYOU_BBS_VERSION 常量正确\n";
        $checks['bbs_version_constant'] = true;
    } else {
        echo "   ✗ BOYOU_BBS_VERSION 常量未找到或不正确\n";
        $errors[] = "BBS版本常量错误";
        $checks['bbs_version_constant'] = false;
    }
}

// 4. 检查配置文件版本
echo "\n4. 检查配置文件版本...\n";
if (file_exists('conf/conf.php')) {
    $content = file_get_contents('conf/conf.php');
    if (strpos($content, "'version' => '6.1.0'") !== false) {
        echo "   ✓ 配置文件版本正确\n";
        $checks['config_version'] = true;
    } else {
        echo "   ✗ 配置文件版本不正确\n";
        $errors[] = "配置文件版本错误";
        $checks['config_version'] = false;
    }
}

// 5. 检查引用更新
echo "\n5. 检查文件引用更新...\n";
$filesToCheck = [
    'index.php' => ['BOYOUPHP_PATH', 'boyouphp/boyouphp.php'],
    'install/index.php' => ['boyouphp/boyouphp.php'],
    'tool/merge.php' => ['../boyouphp/', 'boyouphp.php']
];

foreach ($filesToCheck as $file => $expectedStrings) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $fileOk = true;
        
        foreach ($expectedStrings as $expected) {
            if (strpos($content, $expected) !== false) {
                echo "   ✓ $file 包含 $expected\n";
            } else {
                echo "   ✗ $file 缺少 $expected\n";
                $errors[] = "$file 中缺少 $expected";
                $fileOk = false;
            }
        }
        
        $checks[$file] = $fileOk;
    } else {
        echo "   ✗ $file 不存在\n";
        $errors[] = "$file 不存在";
        $checks[$file] = false;
    }
}

// 6. 检查语言文件更新
echo "\n6. 检查语言文件更新...\n";
if (file_exists('lang/zh-cn/bbs_install.php')) {
    $content = file_get_contents('lang/zh-cn/bbs_install.php');
    if (strpos($content, 'Boyou BBS 6.1') !== false) {
        echo "   ✓ 安装语言文件已更新\n";
        $checks['install_lang'] = true;
    } else {
        echo "   ✗ 安装语言文件未更新\n";
        $errors[] = "安装语言文件未更新";
        $checks['install_lang'] = false;
    }
}

// 7. 尝试包含框架文件
echo "\n7. 测试框架文件包含...\n";
try {
    // 临时设置常量避免重复定义
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', './');
    
    // 尝试包含框架文件
    if (file_exists('boyouphp/boyouphp.min.php')) {
        ob_start();
        include_once 'boyouphp/boyouphp.min.php';
        $output = ob_get_clean();
        
        if (defined('BOYOUPHP_VERSION')) {
            echo "   ✓ 框架文件包含成功，版本: " . BOYOUPHP_VERSION . "\n";
            $checks['framework_include'] = true;
        } else {
            echo "   ✗ 框架文件包含失败\n";
            $errors[] = "框架文件包含失败";
            $checks['framework_include'] = false;
        }
    } else {
        echo "   ✗ boyouphp.min.php 不存在\n";
        $errors[] = "boyouphp.min.php 不存在";
        $checks['framework_include'] = false;
    }
} catch (Exception $e) {
    echo "   ✗ 框架文件包含出错: " . $e->getMessage() . "\n";
    $errors[] = "框架文件包含出错: " . $e->getMessage();
    $checks['framework_include'] = false;
}

// 8. 统计结果
echo "\n=== 验证结果 ===\n";
$totalChecks = count($checks);
$passedChecks = count(array_filter($checks));

echo "总检查项: $totalChecks\n";
echo "通过检查: $passedChecks\n";
echo "失败检查: " . ($totalChecks - $passedChecks) . "\n";

if (empty($errors)) {
    echo "\n🎉 所有检查都通过了！框架重命名和版本更新成功！\n";
    echo "\n主要变更:\n";
    echo "- xiunophp → boyouphp\n";
    echo "- boyouphp.php → boyouphp.php\n";
    echo "- 版本 4.0.4 → 6.1.0\n";
    echo "- 所有引用已更新\n";
} else {
    echo "\n⚠️ 发现以下问题:\n";
    foreach ($errors as $error) {
        echo "  - $error\n";
    }
}

echo "\n=== 验证完成 ===\n";

?>
