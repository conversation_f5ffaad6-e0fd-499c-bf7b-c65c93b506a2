<?php

echo "=== 验证框架转移完成情况 ===\n\n";

// 1. 检查目录状态
echo "1. 检查目录状态...\n";
$xiunophpExists = is_dir('xiunophp');
$boyouphpExists = is_dir('boyouphp');

echo "xiunophp 目录: " . ($xiunophpExists ? "存在" : "不存在") . "\n";
echo "boyouphp 目录: " . ($boyouphpExists ? "存在" : "不存在") . "\n";

if ($boyouphpExists) {
    $boyouFiles = scandir('boyouphp');
    $phpFiles = array_filter($boyouFiles, function($f) { 
        return pathinfo($f, PATHINFO_EXTENSION) === 'php'; 
    });
    echo "boyouphp 目录包含 " . count($phpFiles) . " 个PHP文件\n";
}

// 2. 检查关键文件
echo "\n2. 检查关键文件...\n";
$keyFiles = [
    'boyouphp.php' => '主框架文件',
    'boyouphp.min.php' => '编译版本',
    'db.func.php' => '数据库函数',
    'cache.func.php' => '缓存函数',
    'misc.func.php' => '杂项函数',
    'array.func.php' => '数组函数',
    'image.func.php' => '图片函数',
    'xn_encrypt.func.php' => '加密函数',
    'db_mysql.class.php' => 'MySQL类',
    'db_pdo_mysql.class.php' => 'PDO MySQL类',
    'db_pdo_sqlite.class.php' => 'SQLite类',
    'cache_redis.class.php' => 'Redis缓存类',
    'cache_apc.class.php' => 'APC缓存类',
    'cache_memcached.class.php' => 'Memcached缓存类',
    'cache_mysql.class.php' => 'MySQL缓存类',
    'cache_xcache.class.php' => 'XCache缓存类',
    'cache_yac.class.php' => 'Yac缓存类'
];

$existingFiles = 0;
$missingFiles = [];

foreach ($keyFiles as $file => $description) {
    $path = "boyouphp/$file";
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✓ $file ($description) - " . number_format($size) . " 字节\n";
        $existingFiles++;
    } else {
        echo "✗ $file ($description) - 缺失\n";
        $missingFiles[] = $file;
    }
}

$totalFiles = count($keyFiles);
$completionRate = ($existingFiles / $totalFiles) * 100;

echo "\n文件完整性: $existingFiles/$totalFiles (" . round($completionRate, 1) . "%)\n";

// 3. 检查版本常量
echo "\n3. 检查版本常量...\n";
if (file_exists('boyouphp/boyouphp.php')) {
    $content = file_get_contents('boyouphp/boyouphp.php');
    
    if (strpos($content, "define('BOYOUPHP_VERSION', '6.1')") !== false) {
        echo "✓ BOYOUPHP_VERSION 常量存在\n";
    } else {
        echo "✗ BOYOUPHP_VERSION 常量缺失\n";
    }
    
    if (strpos($content, "define('BOYOU_BBS_VERSION', '6.1')") !== false) {
        echo "✓ BOYOU_BBS_VERSION 常量存在\n";
    } else {
        echo "✗ BOYOU_BBS_VERSION 常量缺失\n";
    }
    
    if (strpos($content, 'BoyouPHP 6.1') !== false) {
        echo "✓ 文件注释已更新为 BoyouPHP 6.1\n";
    } else {
        echo "⚠️  文件注释可能未完全更新\n";
    }
}

// 4. 测试框架加载
echo "\n4. 测试框架加载...\n";
try {
    // 设置必要的常量
    if (!defined('DEBUG')) define('DEBUG', 1);
    if (!defined('APP_PATH')) define('APP_PATH', './');
    if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', './boyouphp/');
    
    // 尝试包含框架
    ob_start();
    include_once 'boyouphp/boyouphp.min.php';
    $output = ob_get_clean();
    
    if (defined('BOYOUPHP_VERSION')) {
        echo "✓ 框架加载成功，版本: " . BOYOUPHP_VERSION . "\n";
    } else {
        echo "⚠️  框架加载但版本常量未定义\n";
    }
    
    // 测试基本函数
    if (function_exists('G')) {
        G('test', 'value');
        if (G('test') === 'value') {
            echo "✓ 全局函数 G() 工作正常\n";
        } else {
            echo "✗ 全局函数 G() 工作异常\n";
        }
    } else {
        echo "✗ 全局函数 G() 不存在\n";
    }
    
    if (function_exists('param')) {
        echo "✓ 参数函数 param() 存在\n";
    } else {
        echo "✗ 参数函数 param() 不存在\n";
    }
    
} catch (Exception $e) {
    echo "✗ 框架加载失败: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "✗ 框架加载错误: " . $e->getMessage() . "\n";
}

// 5. 生成总结报告
echo "\n=== 转移总结 ===\n";

$issues = [];
if (!$boyouphpExists) {
    $issues[] = "boyouphp 目录不存在";
}
if ($completionRate < 100) {
    $issues[] = "有 " . count($missingFiles) . " 个关键文件缺失";
}

if (empty($issues)) {
    echo "🎉 框架转移完全成功！\n";
    echo "\n✅ 所有检查项目都通过了：\n";
    echo "  - boyouphp 目录已创建\n";
    echo "  - 所有关键文件都已转移\n";
    echo "  - 版本信息已更新\n";
    echo "  - 框架可以正常加载\n";
    
    if ($xiunophpExists) {
        echo "\n📋 下一步建议：\n";
        echo "  - 测试网站功能是否正常\n";
        echo "  - 确认无误后可以删除 xiunophp 目录\n";
        echo "  - 更新其他文件中的框架引用\n";
    }
} else {
    echo "⚠️  转移过程中发现以下问题：\n";
    foreach ($issues as $issue) {
        echo "  - $issue\n";
    }
}

if (!empty($missingFiles)) {
    echo "\n缺失的文件：\n";
    foreach ($missingFiles as $file) {
        echo "  - $file\n";
    }
}

echo "\n=== 验证完成 ===\n";

?>
