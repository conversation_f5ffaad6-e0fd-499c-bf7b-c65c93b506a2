<?php

echo "=== 验证 xiuno 到 boyou 的替换结果 ===\n\n";

$replacements = [
    'conf/conf.php' => [
        'Boyou BBS 4.0 配置文件',
        'boyou_bbs',
        'Boyou BBS'
    ],
    'README.md' => [
        'Boyou BBS 4.0 是什么',
        'boyou.js 采用了'
    ],
    'view/htm/footer.inc.htm' => [
        'boyou.js'
    ],
    'admin/view/htm/footer.inc.htm' => [
        'boyou.js'
    ],
    'install/view/htm/footer.inc.htm' => [
        'boyou.js'
    ],
    'view/htm/browser.htm' => [
        'google.com/chrome',
        'mozilla.org/firefox'
    ],
    'view/js/xiuno.js' => [
        'boyou.js 封装了部分',
        'boyou.js loaded'
    ]
];

$issues = [];
$success = 0;

foreach ($replacements as $file => $expectedStrings) {
    echo "检查文件: $file\n";
    
    if (!file_exists($file)) {
        $issues[] = "文件不存在: $file";
        continue;
    }
    
    $content = file_get_contents($file);
    $fileSuccess = true;
    
    foreach ($expectedStrings as $expected) {
        if (strpos($content, $expected) === false) {
            $issues[] = "在 $file 中未找到: $expected";
            $fileSuccess = false;
        } else {
            echo "  ✓ 找到: $expected\n";
        }
    }
    
    if ($fileSuccess) {
        $success++;
    }
    echo "\n";
}

// 检查是否还有残留的xiuno引用
echo "检查残留的 xiuno 引用...\n";
$directories = ['view', 'admin', 'install', 'conf'];
$xiuNoReferences = [];

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && in_array($file->getExtension(), ['php', 'htm', 'js', 'css'])) {
            $content = file_get_contents($file->getPathname());
            
            // 检查xiuno引用（排除注释和已知的保留项）
            if (preg_match_all('/xiuno(?!php)/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $line = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                    $xiuNoReferences[] = [
                        'file' => $file->getPathname(),
                        'line' => $line,
                        'text' => $match[0]
                    ];
                }
            }
        }
    }
}

echo "\n=== 验证结果 ===\n";
echo "成功替换的文件: $success/" . count($replacements) . "\n";

if (!empty($issues)) {
    echo "\n发现的问题:\n";
    foreach ($issues as $issue) {
        echo "  ✗ $issue\n";
    }
}

if (!empty($xiuNoReferences)) {
    echo "\n残留的 xiuno 引用:\n";
    foreach ($xiuNoReferences as $ref) {
        echo "  - {$ref['file']}:{$ref['line']} - {$ref['text']}\n";
    }
} else {
    echo "\n✓ 未发现残留的 xiuno 引用\n";
}

// 检查boyou.js是否存在
if (file_exists('view/js/boyou.js')) {
    echo "\n✓ boyou.js 文件存在\n";
    $size = filesize('view/js/boyou.js');
    echo "  文件大小: " . number_format($size) . " 字节\n";
} else {
    echo "\n✗ boyou.js 文件不存在\n";
    $issues[] = "boyou.js 文件缺失";
}

if (empty($issues) && empty($xiuNoReferences)) {
    echo "\n🎉 所有替换都已成功完成！\n";
} else {
    echo "\n⚠️  还有一些问题需要处理\n";
}

echo "\n=== 验证完成 ===\n";

?>
