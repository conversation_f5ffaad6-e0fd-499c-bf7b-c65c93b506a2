<?php

class cache_redis {
	
	public $conf = array();
	public $link = NULL;
	public $cachepre = '';
	public $errno = 0;
	public $errstr = '';
	
        public function __construct($conf = array()) {
                if(!extension_loaded('Redis')) {
                        return $this->error(-1, ' Redis 扩展没有加载');
                }
                $this->conf = $conf;
		$this->cachepre = isset($conf['cachepre']) ? $conf['cachepre'] : 'pre_';
        }
        public function connect() {
                if($this->link) return $this->link;

                try {
                    $redis = new Redis;

                    // 设置连接超时
                    $timeout = isset($this->conf['timeout']) ? $this->conf['timeout'] : 3;
                    $r = $redis->connect($this->conf['host'], $this->conf['port'], $timeout);

                    if(!$r) {
                        return $this->error(-1, '连接 Redis 服务器失败。');
                    }

                    // 设置密码认证
                    if(!empty($this->conf['password'])) {
                        if(!$redis->auth($this->conf['password'])) {
                            return $this->error(-1, 'Redis 密码认证失败。');
                        }
                    }

                    // 选择数据库
                    if(isset($this->conf['database']) && $this->conf['database'] > 0) {
                        $redis->select($this->conf['database']);
                    }

                    // 设置序列化方式
                    $redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_PHP);

                    $this->link = $redis;
                    return $this->link;

                } catch (Exception $e) {
                    return $this->error(-1, 'Redis 连接异常: ' . $e->getMessage());
                }
        }
        public function set($k, $v, $life = 0) {
                if(!$this->link && !$this->connect()) return FALSE;

                try {
                    $key = $this->cachepre . $k;

                    if($life > 0) {
                        $r = $this->link->setex($key, $life, $v);
                    } else {
                        $r = $this->link->set($key, $v);
                    }

                    return $r;
                } catch (Exception $e) {
                    $this->error(-1, 'Redis set 操作失败: ' . $e->getMessage());
                    return FALSE;
                }
        }
        public function get($k) {
                if(!$this->link && !$this->connect()) return FALSE;

                try {
                    $key = $this->cachepre . $k;
                    $r = $this->link->get($key);
                    return $r === FALSE ? NULL : $r;
                } catch (Exception $e) {
                    $this->error(-1, 'Redis get 操作失败: ' . $e->getMessage());
                    return FALSE;
                }
        }
        public function delete($k) {
                if(!$this->link && !$this->connect()) return FALSE;

                try {
                    $key = $this->cachepre . $k;
                    return $this->link->del($key) > 0;
                } catch (Exception $e) {
                    $this->error(-1, 'Redis delete 操作失败: ' . $e->getMessage());
                    return FALSE;
                }
        }
        public function truncate() {
                if(!$this->link && !$this->connect()) return FALSE;
                return $this->link->flushdb(); // flushall
        }
        public function error($errno = 0, $errstr = '') {
		$this->errno = $errno;
		$this->errstr = $errstr;
		DEBUG AND trigger_error('Cache Error:'.$this->errstr);
	}
        public function __destruct() {

        }
}

?>