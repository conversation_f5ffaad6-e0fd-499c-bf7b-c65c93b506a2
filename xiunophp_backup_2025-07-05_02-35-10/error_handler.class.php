<?php

/**
 * 现代化错误处理类
 * 提供结构化错误处理、日志记录、异常管理
 */
class error_handler {
    
    private static ?self $instance = null;
    private array $errorLevels = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING', 
        E_PARSE => 'PARSE',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_CORE_WARNING => 'CORE_WARNING',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE',
        E_STRICT => 'STRICT',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER_DEPRECATED'
    ];
    
    private array $errorLog = [];
    private bool $debugMode = false;
    
    private function __construct() {
        $this->debugMode = defined('DEBUG') && DEBUG > 0;
    }
    
    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 注册错误处理器
     * @return void
     */
    public function register(): void {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }
    
    /**
     * 处理PHP错误
     * @param int $errno 错误级别
     * @param string $errstr 错误信息
     * @param string $errfile 错误文件
     * @param int $errline 错误行号
     * @return bool
     */
    public function handleError(int $errno, string $errstr, string $errfile, int $errline): bool {
        // 如果错误被@符号抑制，则不处理
        if (!(error_reporting() & $errno)) {
            return false;
        }
        
        $errorType = $this->errorLevels[$errno] ?? 'UNKNOWN';
        
        $error = [
            'type' => $errorType,
            'message' => $errstr,
            'file' => $errfile,
            'line' => $errline,
            'time' => date('Y-m-d H:i:s'),
            'trace' => $this->getSimpleTrace()
        ];
        
        $this->logError($error);
        
        // 在调试模式下显示错误
        if ($this->debugMode) {
            $this->displayError($error);
        }
        
        // 对于致命错误，终止执行
        if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $this->handleFatalError($error);
        }
        
        return true;
    }
    
    /**
     * 处理未捕获的异常
     * @param Throwable $exception
     * @return void
     */
    public function handleException(Throwable $exception): void {
        $error = [
            'type' => 'EXCEPTION',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'time' => date('Y-m-d H:i:s'),
            'trace' => $exception->getTraceAsString(),
            'class' => get_class($exception)
        ];
        
        $this->logError($error);
        
        if ($this->debugMode) {
            $this->displayError($error);
        } else {
            $this->displayUserFriendlyError();
        }
    }
    
    /**
     * 处理脚本关闭时的致命错误
     * @return void
     */
    public function handleShutdown(): void {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $formattedError = [
                'type' => $this->errorLevels[$error['type']] ?? 'FATAL',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'time' => date('Y-m-d H:i:s'),
                'trace' => 'Fatal error - no trace available'
            ];
            
            $this->logError($formattedError);
            
            if ($this->debugMode) {
                $this->displayError($formattedError);
            } else {
                $this->displayUserFriendlyError();
            }
        }
    }
    
    /**
     * 记录错误到日志
     * @param array $error 错误信息
     * @return void
     */
    private function logError(array $error): void {
        $this->errorLog[] = $error;
        
        // 写入文件日志
        $logMessage = sprintf(
            "[%s] %s: %s in %s on line %d\n",
            $error['time'],
            $error['type'],
            $error['message'],
            $error['file'],
            $error['line']
        );
        
        if (function_exists('xn_log')) {
            xn_log($logMessage, 'error');
        } else {
            error_log($logMessage);
        }
    }
    
    /**
     * 显示错误信息（调试模式）
     * @param array $error 错误信息
     * @return void
     */
    private function displayError(array $error): void {
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'type' => $error['type'],
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line']
            ]);
        } else {
            echo "<div style='background:#f8d7da;color:#721c24;padding:10px;margin:10px;border:1px solid #f5c6cb;border-radius:4px;'>";
            echo "<h4>{$error['type']}</h4>";
            echo "<p><strong>Message:</strong> {$error['message']}</p>";
            echo "<p><strong>File:</strong> {$error['file']} on line {$error['line']}</p>";
            if (isset($error['trace'])) {
                echo "<details><summary>Stack Trace</summary><pre>{$error['trace']}</pre></details>";
            }
            echo "</div>";
        }
    }
    
    /**
     * 显示用户友好的错误信息
     * @return void
     */
    private function displayUserFriendlyError(): void {
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => '服务器内部错误，请稍后重试'
            ]);
        } else {
            echo "<h1>服务器错误</h1><p>抱歉，服务器遇到了一个错误。请稍后重试。</p>";
        }
    }
    
    /**
     * 处理致命错误
     * @param array $error 错误信息
     * @return never
     */
    private function handleFatalError(array $error): never {
        if ($this->debugMode) {
            $this->displayError($error);
        } else {
            $this->displayUserFriendlyError();
        }
        exit(1);
    }
    
    /**
     * 获取简化的调用栈
     * @return string
     */
    private function getSimpleTrace(): string {
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
        $result = [];
        
        foreach ($trace as $item) {
            if (isset($item['file']) && isset($item['line'])) {
                $result[] = basename($item['file']) . ':' . $item['line'];
            }
        }
        
        return implode(' -> ', $result);
    }
    
    /**
     * 获取错误日志
     * @return array
     */
    public function getErrorLog(): array {
        return $this->errorLog;
    }
    
    /**
     * 清空错误日志
     * @return void
     */
    public function clearErrorLog(): void {
        $this->errorLog = [];
    }
    
    /**
     * 获取错误统计
     * @return array
     */
    public function getErrorStats(): array {
        $stats = [];
        
        foreach ($this->errorLog as $error) {
            $type = $error['type'];
            if (!isset($stats[$type])) {
                $stats[$type] = 0;
            }
            $stats[$type]++;
        }
        
        return $stats;
    }
}
